{"id": "AdtCs47Lt1Wtf2Dt", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a", "templateCredsSetupCompleted": true}, "name": "Upload-Post And FLUX Kontext", "tags": [], "nodes": [{"id": "ed7d240f-9181-4f23-9e3a-24e7886c7e31", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-180, 0], "parameters": {}, "typeVersion": 1}, {"id": "4656ae65-d58b-4126-ada3-eda97fde5c4e", "name": "Is Ready?", "type": "n8n-nodes-base.if", "position": [500, 400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "3cf5b451-9ff5-4c2a-864f-9aa7d286871a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "Ready"}, {"id": "a9b6d102-bb38-443f-a204-1c07476cb360", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $runIndex }}", "rightValue": 5}]}}, "typeVersion": 2.2}, {"id": "f8dbc367-1052-4548-b3b8-33f6ede4d0ba", "name": "Check FLUX status", "type": "n8n-nodes-base.httpRequest", "position": [340, 400], "parameters": {"url": "https://api.bfl.ml/v1/get_result", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "id", "value": "={{ $json.id }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "4eQN9wBw8SniKcPw", "name": "bfl-FLUX"}}, "typeVersion": 4.2}, {"id": "9624cdc6-1f7b-459f-b16a-eab69d23b45c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-60, 320], "parameters": {"color": 7, "width": 862, "height": 282, "content": "# Image processing part"}, "typeVersion": 1}, {"id": "c6393bc7-6b7a-4a25-a524-8a7bd6b2c781", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-380, 400], "parameters": {"inputSource": "passthrough"}, "typeVersion": 1.1}, {"id": "4f3717a6-2580-4664-8182-efa1c8652589", "name": "FLUX Kontext", "type": "n8n-nodes-base.httpRequest", "position": [0, 400], "parameters": {"url": "https://api.bfl.ml/v1/flux-kontext-pro", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "input_image", "value": "={{ $json[$('When Executed by Another Workflow').first().json.binaryin] }}"}, {"name": "prompt", "value": "={{ $('When Executed by Another Workflow').first().json.prompt }}"}, {"name": "prompt_upsampling", "value": "={{ false }}"}, {"name": "output_format", "value": "png"}, {"name": " aspect_ratio", "value": "1:1"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "4eQN9wBw8SniKcPw", "name": "bfl-FLUX"}}, "typeVersion": 4.2}, {"id": "2a48ecd4-07bc-4202-abe3-3bc0bfbfed6d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [420, 0], "parameters": {"mode": "combine", "options": {"includeUnpaired": true}, "combineBy": "combineByPosition"}, "typeVersion": 3.1}, {"id": "cc5f96a9-ed87-4526-85c2-074dc5c8e166", "name": "Wait 2 sec", "type": "n8n-nodes-base.wait", "position": [180, 400], "webhookId": "3df5a762-edab-49df-ac70-be3b56545212", "parameters": {"amount": 2}, "typeVersion": 1.1}, {"id": "54cf0d0d-c3a0-4243-b425-ba8cdfaf1063", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, -240], "parameters": {"width": 380, "height": 260, "content": "## Define prompts here\nPrepare an array of prompts that will be used one by one on the next steps. Update limit in the `Number of Steps` node if you need more than 5 iterations."}, "typeVersion": 1}, {"id": "add02353-b31e-4990-83ad-2d936343ab7f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-20, 60], "parameters": {"width": 380, "height": 220, "content": "## Load the initial image"}, "typeVersion": 1}, {"id": "8367ef9f-cf00-4987-91ec-09fe3ec6918d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-340, -420], "parameters": {"width": 260, "height": 400, "content": "## Initial example\n![](https://raw.githubusercontent.com/teds-tech-talks/n8n-community-leaderboard/main/_creators/eduard/mascot.png)"}, "typeVersion": 1}, {"id": "c4a52815-ba73-4f9c-8c44-fca40cab6440", "name": "Get File from GitHub", "type": "n8n-nodes-base.github", "position": [20, 120], "webhookId": "e70d0c46-6478-4c11-85a6-8b7e1f9588de", "parameters": {"owner": {"__rl": true, "mode": "name", "value": "teds-tech-talks"}, "filePath": "_creators/eduard/mascot.png", "resource": "file", "operation": "get", "repository": {"__rl": true, "mode": "name", "value": "n8n-community-leaderboard"}, "authentication": "oAuth2", "asBinaryProperty": false, "additionalParameters": {}}, "credentials": {"githubOAuth2Api": {"id": "SBUgEkMg70oFZRct", "name": "TTT GitHub account"}}, "typeVersion": 1.1}, {"id": "bb48cde8-fc79-407e-8daa-4322398d8385", "name": "Download Initial Image", "type": "n8n-nodes-base.httpRequest", "position": [220, 120], "parameters": {"url": "={{ $json.download_url }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "data0"}}}}, "typeVersion": 4.2}, {"id": "ffc484fe-c183-4858-8303-8733596e2afc", "name": "If", "type": "n8n-nodes-base.if", "position": [680, 0], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4d3dcb8f-c435-40fd-aca6-a37647fc3a10", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $runIndex }}", "rightValue": "={{ $json.Steps }}"}]}}, "typeVersion": 2.2}, {"id": "6fbd6310-7596-4460-8a85-3cc0b1253509", "name": "Number of Steps", "type": "n8n-nodes-base.set", "position": [220, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9e3d00d0-467a-41c8-aaa1-f1d29694737c", "name": "Steps", "type": "number", "value": "={{ Math.min($json.Prompts.length,5) }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "6bacc1b2-7154-428c-ab0f-76f356c59e35", "name": "All Prompts", "type": "n8n-nodes-base.set", "position": [20, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "64e9c28e-a60a-499b-aa89-177bf952959f", "name": "Prompts", "type": "array", "value": "=[\"Make this animal mascot coming out of the builing with wearing an official business suite. Preserve the exact character features and style\",\"Now make this animal mascot sitting in the terrace mediterranian cafe. Preserve the exact character features and style, but wearing hawaii shorts instead of trouses\",\"Now make this animal mascot standing on the beach with a starfish in it's hands, waring only hawaii shorts. Preserve the exact character features and style.\"]"}]}}, "typeVersion": 3.4}, {"id": "913b8c79-e9ed-4221-853a-2ea6071f8c5a", "name": "Run FLUX", "type": "n8n-nodes-base.executeWorkflow", "position": [1120, -100], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "AdtCs47Lt1Wtf2Dt", "cachedResultName": "Upload-Post And FLUX Kontext"}, "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "8c7089e9-45ea-4626-9c7d-01dc9df2137e", "name": "Current Step", "type": "n8n-nodes-base.set", "position": [880, 0], "parameters": {"include": "selected", "options": {}, "assignments": {"assignments": [{"id": "64e9c28e-a60a-499b-aa89-177bf952959f", "name": "prompt", "type": "string", "value": "={{ $json.Prompts[$runIndex] }}"}, {"id": "aee3c455-ced2-4520-a088-18ffaf6db11c", "name": "binaryin", "type": "string", "value": "=data{{ $runIndex }}"}, {"id": "61cccca2-7b0c-4ea6-82f3-5dc2a377c3cb", "name": "binaryout", "type": "string", "value": "=data{{ Number($runIndex)+1 }}"}, {"id": "0cbe4309-e6f0-4c65-87f6-95bf6609aade", "name": "currentstep", "type": "number", "value": "={{ $runIndex }}"}]}, "includeFields": "Prompts, Steps", "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "8eb4790f-ecf9-4136-813a-a87bd1d40edd", "name": "Merge3", "type": "n8n-nodes-base.merge", "position": [1320, -20], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3.2}, {"id": "49eb0be4-4ebc-48bd-a77d-20fb02779ad1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [620, -200], "parameters": {"width": 860, "height": 380, "content": "## Iterate over prompts\n- On each step a next prompt it taken from the original array\n- Outputs from the previous FLUX Kontext request are moved forward to the subsequent generation"}, "typeVersion": 1}, {"id": "361cd8ac-29be-40a9-a5f8-d12656373c96", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1100, 220], "parameters": {"color": 5, "width": 260, "height": 280, "content": "## [Post several images via Upload Post](https://www.upload-post.com/?linkId=lp_144414&sourceId=post-now&tenantId=upload-post-app)\n[Click to create your own account](https://www.upload-post.com/?linkId=lp_144414&sourceId=post-now&tenantId=upload-post-app)"}, "typeVersion": 1}, {"id": "af34e5a7-d1cc-446f-a210-5aa96c271640", "name": "Upload Post", "type": "n8n-nodes-upload-post.uploadPost", "position": [1120, 340], "parameters": {"user": "Ed", "title": "Testing n8n uploads with Upload Post community node hotfix and FLUX1. Kontext", "photos": "={{ Array.from({length: $json.Steps}, (_, i) => `data${i + 1}`).join(',') }}", "platform": ["x"]}, "credentials": {"uploadPostApi": {"id": "xLY6thqjSJUCWssD", "name": "Upload Post account"}}, "typeVersion": 1}, {"id": "********-6a7d-4e1a-9fc4-74d6f5f78187", "name": "Image to Base64", "type": "n8n-nodes-base.extractFromFile", "position": [-200, 400], "parameters": {"options": {}, "operation": "binaryToPropery", "destinationKey": "={{ $('When Executed by Another Workflow').first().json.binaryin }}", "binaryPropertyName": "={{ $('When Executed by Another Workflow').first().json.binaryin }}"}, "typeVersion": 1}, {"id": "d40118c7-0ab6-4276-b733-22419e5521d6", "name": "Get Image", "type": "n8n-nodes-base.httpRequest", "position": [680, 400], "parameters": {"url": "={{ $json.result.sample }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "={{ $('When Executed by Another Workflow').first().json.binaryout }}"}}}}, "typeVersion": 4.2}, {"id": "e35f7544-b687-4ee5-a59f-3f98d26b1d12", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [620, -620], "parameters": {"width": 260, "height": 400, "content": "## Step 1\n![](https://pbs.twimg.com/media/Gs7SwU_XYAASZBn?format=jpg&name=medium)"}, "typeVersion": 1}, {"id": "230498f5-69f5-4282-bb25-854720b1d536", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [920, -620], "parameters": {"width": 260, "height": 400, "content": "## Step 2\n![](https://pbs.twimg.com/media/Gs7SwovWQAAncQT?format=jpg&name=medium)"}, "typeVersion": 1}, {"id": "425c3696-4532-401e-8131-78553358c8c8", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1220, -620], "parameters": {"width": 260, "height": 400, "content": "## Step 3\n![](https://pbs.twimg.com/media/Gs7SxHsXYAAkhPF?format=jpg&name=medium)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveDataSuccessExecution": "all"}, "versionId": "94bff334-aee9-4139-acb9-966013fca4ea", "connections": {"If": {"main": [[{"node": "Current Step", "type": "main", "index": 0}], [{"node": "Upload Post", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Merge3": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Run FLUX": {"main": [[{"node": "Merge3", "type": "main", "index": 0}]]}, "Is Ready?": {"main": [[{"node": "Get Image", "type": "main", "index": 0}], [{"node": "Wait 2 sec", "type": "main", "index": 0}]]}, "Wait 2 sec": {"main": [[{"node": "Check FLUX status", "type": "main", "index": 0}]]}, "All Prompts": {"main": [[{"node": "Number of Steps", "type": "main", "index": 0}]]}, "Current Step": {"main": [[{"node": "Run FLUX", "type": "main", "index": 0}, {"node": "Merge3", "type": "main", "index": 1}]]}, "FLUX Kontext": {"main": [[{"node": "Wait 2 sec", "type": "main", "index": 0}]]}, "Image to Base64": {"main": [[{"node": "FLUX Kontext", "type": "main", "index": 0}]]}, "Number of Steps": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Check FLUX status": {"main": [[{"node": "Is Ready?", "type": "main", "index": 0}]]}, "Get File from GitHub": {"main": [[{"node": "Download Initial Image", "type": "main", "index": 0}]]}, "Download Initial Image": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Image to Base64", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "Get File from GitHub", "type": "main", "index": 0}, {"node": "All Prompts", "type": "main", "index": 0}]]}}}