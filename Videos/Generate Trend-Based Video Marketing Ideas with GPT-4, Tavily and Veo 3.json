{"id": "", "meta": {}, "name": "Veo3 AI Marketing Agent", "tags": [], "nodes": [{"id": "1c63ed32-09fa-4f50-9821-f4cbe7b51d09", "name": "Idea Gen Agent (research)", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-20, -40], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4", "cachedResultName": "GPT-4"}, "options": {}, "messages": {"values": [{"content": "Create an impactful marketing video"}, {"role": "system", "content": "You are a creative marketing strategist for an e-commerce brand called <PERSON>’s Closet, which sells women’s wear and accessories online.\n\nEach day, your job is to generate one fresh, engaging marketing video idea that ties together:\n\nA trending topic or recent news event, and\n\nA featured product or collection from <PERSON>’s Closet (e.g. floral dresses, pastel handbags, silk scarves).\n\nUse Tavily to research current lifestyle trends, viral moments on social media, fashion-related events, or pop culture news from the past 24–48 hours.\nThen, creatively link that insight to a product theme or customer emotion (e.g. confidence, elegance, boldness) to craft a short, punchy marketing video concept.\n\nOutput format (for daily automation):\nVideo Idea Title: A catchy title (under 10 words)\n\nTrend or News Inspiration: Brief summary of the trend/topic found\n\nHow It Connects to <PERSON>’s Closet: Why this is relevant to the audience and which product(s) to feature\n\nSuggested Hook for Video: 1-line attention-grabber\n\nCall-to-Action: What the viewer should do next (e.g. “Shop the look”, “Limited drop”, etc.)"}]}}, "credentials": {}, "typeVersion": 1.8}, {"id": "2c6514c9-7906-4721-b9a1-5fc665e24462", "name": "<PERSON><PERSON>", "type": "@tavily/n8n-nodes-tavily.tavilyTool", "position": [140, 160], "parameters": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Query', ``, 'string') }}", "options": {}}, "credentials": {}, "typeVersion": 1}, {"id": "20ef07ca-9bdf-4bc9-913b-0d9f9b7ac594", "name": "Video Prompt Agent", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [340, -40], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4", "cachedResultName": "GPT-4"}, "options": {}, "messages": {"values": [{"content": "={{ $json.message.content }}"}, {"role": "system", "content": "You are a creative video concept generator trained to write highly effective prompts for Veo3, an AI video generation tool.\n\nYour task is to take the user's input — a short video idea or concept — and turn it into a compelling, cinematic-style prompt that Veo3 can use to generate a <5 second marketing video.\n\nYour output should:\n\nBe fewer than 100 words.\n\nClearly describe the visual scene and action.\n\nUse rich sensory and emotional language (e.g. vibrant, dramatic, elegant, playful).\n\nInclude camera angles or transitions (if applicable), e.g. \"slow zoom-in\", \"swooping drone shot\", \"quick cut\", etc.\n\nSpecify product or brand visuals to appear (e.g. “logo reveal at the end”, “woman applying lipstick with brand name glowing behind her”).\n\nTailor the tone to match the goal: e.g. playful for Gen Z fashion, sleek for luxury goods, bold for tech, etc.\n\nFormat your output like this:\n\n🎬 Veo3 Prompt:\n[Your cinematic, descriptive video prompt here]\n\nExample user input:\n\"Promote a new eco-friendly water bottle line for gym goers\"\n\nExample output:\n🎬 Veo3 Prompt:\nIn a dimly lit gym, slow-motion of sweat dripping as a toned athlete finishes a set. A crisp “click” as they open a sleek matte-black eco bottle. Water vapor swirls as they drink. Quick cut to logo reveal: “HydraLoop — Power Sustainably.” Glowing green pulse animates behind the text."}]}}, "credentials": {}, "typeVersion": 1.8}, {"id": "15114238-52f2-4ea1-baa7-e8f70c5fa6ab", "name": "FAL Veo 3 Post Request", "type": "n8n-nodes-base.httpRequest", "position": [700, -40], "parameters": {"url": "https://queue.fal.run/fal-ai/veo3", "method": "POST", "options": {}, "jsonBody": "={{ { \"prompt\": $json.message.content } }}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {}, "typeVersion": 4.2}, {"id": "2abf648d-24f3-4e21-b40c-fb7a7058c557", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [920, -40], "webhookId": "3c49ce0b-6628-41b3-9bba-f993f920ab75", "parameters": {"amount": 90}, "typeVersion": 1.1}, {"id": "323c0d88-c792-44f3-8f39-3f9e349511ab", "name": "Get Video  Status from FAL / Veo 3", "type": "n8n-nodes-base.httpRequest", "position": [1140, -40], "parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $json.request_id }}/status", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {}, "typeVersion": 4.2}, {"id": "e7513b78-4408-4b85-863f-2b7740660679", "name": "Get Video URL request", "type": "n8n-nodes-base.httpRequest", "position": [1540, -40], "parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $json.request_id }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {}, "typeVersion": 4.2}, {"id": "0c2937f5-b5a7-4a1a-baf3-084bb4cebfca", "name": "If", "type": "n8n-nodes-base.if", "position": [1340, -40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "180190c9-6fcb-4d19-ade4-f221e99ffc82", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "COMPLETED"}]}}, "typeVersion": 2.2}, {"id": "9d37835a-a267-4d6f-8b51-459b31a02f0e", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [1720, -40], "webhookId": "54c66ae2-92e4-4735-8ba7-80536d4778c5", "parameters": {"sendTo": "REDACTED_EMAIL", "message": "={{ $json.video.url }}", "options": {}, "subject": "Sally's Closet Marketing Video", "emailType": "text"}, "credentials": {}, "typeVersion": 2.1}, {"id": "ac639284-66c4-4973-8ed4-c039c9156da9", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-220, -40], "parameters": {"rule": {"interval": [{"triggerAtHour": 10}]}}, "typeVersion": 1.2}, {"id": "579e6dda-a4db-4473-90da-0ca520131daa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-60, -260], "parameters": {"color": 3, "width": 700, "height": 560, "content": "Ideation & Prompt Generation"}, "typeVersion": 1}, {"id": "8210a853-3f91-483b-807c-0a7ca897e152", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-320, -260], "parameters": {"height": 560, "content": "Scheduled Trigger"}, "typeVersion": 1}, {"id": "2912e1f5-3d8d-444d-ac25-6d99b1102222", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [660, -260], "parameters": {"color": 4, "width": 200, "height": 560, "content": "Request Video"}, "typeVersion": 1}, {"id": "2fd8f905-aea1-4e16-a742-927662930c8e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [880, -260], "parameters": {"color": 6, "width": 800, "height": 560, "content": "Check & Get Result Loop"}, "typeVersion": 1}, {"id": "49413a91-c5db-43c8-81fc-951d0d9e9d29", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1700, -260], "parameters": {"color": 2, "width": 280, "height": 560, "content": "Send to Email"}, "typeVersion": 1}, {"id": "dab2f0aa-5d71-465b-8c70-2ab2522eeed6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-320, 360], "parameters": {"color": 5, "width": 1320, "height": 900, "content": "🎬 AI Video Marketing Agent (Veo 3 + GPT-4 + <PERSON>ly + Gmail)\nAutomate your entire video content creation pipeline with this AI-powered, no-code workflow built in n8n.\n\nThis template connects a suite of smart tools to help you generate scroll-stopping short video ideas based on daily trending topics and auto-deliver them via email—ready for production in Veo 3.\n\n🔧 How it works:\nScheduled Trigger (Daily)\nKicks off the process each day at your chosen time.\n\nTavily Agent (Web Search)\nSearches the latest trends, viral moments, or market news based on your e-commerce brand (e.g. “Sally’s Closet”).\n\nOpenAI GPT-4 Agent (Creative Brainstorming)\nGenerates high-conversion marketing video ideas based on your brand’s tone and what’s trending.\n\nPrompt Formatter for Veo 3\nConverts the idea into a cinematic-style prompt, optimized for Veo’s video generation engine (via FAL API).\n\nSend via Gmail\nThe final Veo 3 prompt is emailed to you or your creative team for immediate use or manual refinement.\n\nWatch full step-by-step Tutorial Build Video: https://youtu.be/x7nHpcggpX8\n\n🧠 Use Cases:\nE-commerce brands that need fresh daily content\n\nMarketing teams looking to automate creative ideation\n\nSolopreneurs building a lean video production engine\n\nAnyone experimenting with Veo 3 prompt-based storytelling\n\n🛠️ Tools used:\nn8n Scheduled Trigger\n\nTavily Node (for real-time web search)\n\nLangchain Agent (GPT-4 via OpenAI)\n\nFAL API (Veo 3 prompt delivery)\n\nGmail Node (send final output)\n\n⚡️ Ready-to-use. Fully editable. Zero coding required.\n\n💡 Pro Tip: You can hook this up with the Veo 3 generation API (FAL) to complete the automation end-to-end!"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "", "connections": {"If": {"main": [[{"node": "Get Video URL request", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Get Video  Status from FAL / Veo 3", "type": "main", "index": 0}]]}, "Tavily": {"ai_tool": [[{"node": "Idea Gen Agent (research)", "type": "ai_tool", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Idea Gen Agent (research)", "type": "main", "index": 0}]]}, "Video Prompt Agent": {"main": [[{"node": "FAL Veo 3 Post Request", "type": "main", "index": 0}]]}, "Get Video URL request": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "FAL Veo 3 Post Request": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Idea Gen Agent (research)": {"main": [[{"node": "Video Prompt Agent", "type": "main", "index": 0}]]}, "Get Video  Status from FAL / Veo 3": {"main": [[{"node": "If", "type": "main", "index": 0}]]}}}