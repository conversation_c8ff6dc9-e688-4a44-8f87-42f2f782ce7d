{"id": "HBUhVkSsjslXAojw", "meta": {"instanceId": "1e003a7ea4715b6b35e9947791386a7d07edf3b5bf8d4c9b7ee4fdcbec0447d7"}, "name": "Motion-illustration Workflow Generated with Midjourney and Kling API", "tags": [], "nodes": [{"id": "963603c8-dbe5-4d07-bd1e-74518ddd7a4c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1780, -80], "parameters": {}, "typeVersion": 1}, {"id": "76717322-4eee-483b-9ab9-dd4e9b0f510a", "name": "Kling Video Generator", "type": "n8n-nodes-base.httpRequest", "position": [-820, -180], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"kling\",\n    \"task_type\": \"video_generation\",\n    \"input\": {\n        \"version\": \"1.6\",\n        \n        \"image_url\":\"{{ $json.random_temp_url }}\",\n\n        \"prompt\": \"A young girl sits on a sunlit grassy meadow, gently petting a fluffy white rabbit\"\n       \n    }\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "842c1874-47ab-4efb-baad-155071fd29bb", "name": "Get Video", "type": "n8n-nodes-base.httpRequest", "position": [-620, -60], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "9f36d2ba-ea56-48b8-9c83-60d741c394cb", "name": "Get Image", "type": "n8n-nodes-base.code", "position": [-1000, -180], "parameters": {"jsCode": "// JavaScript Code for Function Node\nreturn {\n  random_temp_url: $input.all()[0].json.data.output.temporary_image_urls[\n    Math.floor(Math.random() * $input.all()[0].json.data.output.temporary_image_urls.length)\n  ]\n};"}, "typeVersion": 2}, {"id": "14995fd1-937a-4e82-a2bb-19dbb65773c4", "name": "Basic Prompt", "type": "n8n-nodes-base.httpRequest", "position": [-1560, -80], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "{\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"A gentle girl and a fluffy rabbit explore a sunlit forest together, playing by a sparkling stream. Butterflies flutter around them as golden sunlight filters through green leaves. Warm and peaceful atmosphere, 4K nature documentary style. --s 500 --sref 4028286908  --niji 6\",\n    \"aspect_ratio\": \"1:1\",\n    \"process_mode\": \"turbo\",\n    \"skip_prompt_check\": false\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "791dae4a-4d99-4bdf-a259-20d3df12b92c", "name": "Get Data Status", "type": "n8n-nodes-base.if", "position": [-1180, -80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a0f8758e-d6fd-44f8-bd79-bc3c4dceddcf", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}, "typeVersion": 2.2}, {"id": "adb13639-1dd9-45af-be7e-c99b6b1219f3", "name": "Wait for Image Generation", "type": "n8n-nodes-base.wait", "position": [-1220, 200], "webhookId": "f3bcf634-8c4b-4bf9-a7f2-d4ee369f5349", "parameters": {}, "typeVersion": 1.1}, {"id": "58ad9c2d-fad7-471b-ad5d-f248b3cfbe29", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1820, -300], "parameters": {"width": 280, "content": "## Motion-illustration\nThis workflow is primarily designed to generate dynamic illustrations for content creators and social media professionals with APIs provided by PiAPI."}, "typeVersion": 1}, {"id": "2571d9ea-1f32-49b0-84da-ad12177714f3", "name": "Midjourney Image Generator", "type": "n8n-nodes-base.httpRequest", "position": [-1360, -80], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "159df2d3-6c5d-436d-b229-1b3d527daf48", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1820, 160], "parameters": {"width": 360, "height": 200, "content": "## Step-by-step Instruction\n1. Fill in your x-api-key of your PiAPI account in the Midjourney Image Generator and Kling Video Generator nodes.\n2. Enter your desired image prompt in **Basic Prompt** node.\n3. Click Test workflow."}, "typeVersion": 1}, {"id": "00de8ec3-102b-41b4-9839-e8fc8cd48253", "name": "Wait for Video Generation", "type": "n8n-nodes-base.wait", "position": [-440, 200], "webhookId": "c7b2590d-96a3-4c7c-8821-3023fead254b", "parameters": {"amount": 20}, "typeVersion": 1.1}, {"id": "75531dff-04d5-4439-ae04-3291ef9cfcde", "name": "Get Final Video URL", "type": "n8n-nodes-base.code", "position": [-140, 80], "parameters": {"jsCode": "// Process the entire response\nreturn {\n  video_url: $input.all()[0].json.data.output.video_url,\n  watermark_free_url: $input.all()[0].json.data.output.works[0].video.resource_without_watermark\n};"}, "typeVersion": 2}, {"id": "1fe883e9-64ee-4bec-8b12-238251089df3", "name": "Verify Data Status", "type": "n8n-nodes-base.if", "position": [-440, -60], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f36fa981-22e0-46db-af8c-c2ac55242c27", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "7f0854bb-7c13-4e67-ba32-809959f47647", "connections": {"Get Image": {"main": [[{"node": "Kling Video Generator", "type": "main", "index": 0}]]}, "Get Video": {"main": [[{"node": "Verify Data Status", "type": "main", "index": 0}]]}, "Basic Prompt": {"main": [[{"node": "Midjourney Image Generator", "type": "main", "index": 0}]]}, "Get Data Status": {"main": [[{"node": "Get Image", "type": "main", "index": 0}], [{"node": "Wait for Image Generation", "type": "main", "index": 0}]]}, "Verify Data Status": {"main": [[{"node": "Get Final Video URL", "type": "main", "index": 0}], [{"node": "Wait for Video Generation", "type": "main", "index": 0}]]}, "Kling Video Generator": {"main": [[{"node": "Get Video", "type": "main", "index": 0}]]}, "Wait for Image Generation": {"main": [[{"node": "Midjourney Image Generator", "type": "main", "index": 0}]]}, "Wait for Video Generation": {"main": [[{"node": "Get Video", "type": "main", "index": 0}]]}, "Midjourney Image Generator": {"main": [[{"node": "Get Data Status", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Basic Prompt", "type": "main", "index": 0}]]}}}