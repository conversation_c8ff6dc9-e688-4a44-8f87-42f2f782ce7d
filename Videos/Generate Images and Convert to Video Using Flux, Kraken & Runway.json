{"id": "Glb8ROS3mY24Bf3t", "meta": {"instanceId": "b3c467df4053d13fe31cc98f3c66fa1d16300ba750506bfd019a0913cec71ea3"}, "name": "generate Image and convert to video", "tags": [], "nodes": [{"id": "ab2da12f-7300-4262-ad91-3e2a258c326b", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [-380, 280], "webhookId": "c97f2119-0ee3-495f-9b58-dd6ee2cd1a2d", "parameters": {"amount": 15}, "typeVersion": 1.1}, {"id": "b7765150-a147-4ee8-adee-b3aeba2f13ed", "name": "get_image_url", "type": "n8n-nodes-base.httpRequest", "position": [-760, 520], "parameters": {"url": "=https://api.us1.bfl.ai/v1/get_result?id={{ $('generate image (flux)').item.json.id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-key", "value": "\"your-key\""}]}}, "typeVersion": 4.2}, {"id": "55428fc5-0b6f-4ea2-9281-9a77af413a5c", "name": "get_image", "type": "n8n-nodes-base.httpRequest", "position": [-560, 520], "parameters": {"url": "={{ $json.result.sample }}", "options": {"response": {"response": {"fullResponse": true, "responseFormat": "file"}}}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "auth", "value": "\"your-api-key\""}]}}, "typeVersion": 4.2}, {"id": "a95d01cd-aae3-4634-83fb-ff2652eb6ca7", "name": "upload to kraken", "type": "n8n-nodes-base.httpRequest", "position": [-380, 520], "parameters": {"url": "https://api.kraken.io/v1/upload", "method": "POST", "options": {"redirect": {"redirect": {}}}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "data", "value": "{\"auth\":{\"api_key\": \"your_api_key\", \"api_secret\": \"your_api_secret\"}, \"wait\":true}"}, {"name": "upload", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}}, "typeVersion": 4.2}, {"id": "779f794a-656a-4f3e-8cbe-b0f418c2c05f", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-760, 280], "parameters": {}, "typeVersion": 1}, {"id": "c4c0c0e8-a830-4905-9983-86002785b9b1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-860, 200], "parameters": {"color": 5, "width": 740, "height": 520, "content": "## Generate Image: Using Flux (Blackforest labs Option)"}, "typeVersion": 1}, {"id": "16d9dc4b-a29f-482a-8d4a-cdec3a9771f7", "name": "Get Image3", "type": "n8n-nodes-base.httpRequest", "position": [500, 280], "parameters": {"url": "={{ $json.final_result[0].origin }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-rapidapi-key", "value": "\"your-rapid-api-key\""}]}}, "typeVersion": 4.2}, {"id": "6949ea7b-f242-49c6-9857-68baa196c31a", "name": "upload to kraken1", "type": "n8n-nodes-base.httpRequest", "position": [680, 280], "parameters": {"url": "https://api.kraken.io/v1/upload", "method": "POST", "options": {"redirect": {"redirect": {}}}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "data", "value": "{\"auth\":{\"api_key\": \"your_api_key\", \"api_secret\": \"your_api_secret\"}, \"wait\":true}"}, {"name": "upload", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}}, "typeVersion": 4.2}, {"id": "ee9492ec-0c71-4961-88c1-18e365a7dc54", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [200, 200], "parameters": {"color": 6, "width": 740, "height": 260, "content": "## Flux (Rapid API Endpoint)"}, "typeVersion": 1}, {"id": "f317d698-6218-4507-b864-7e42f3bbec40", "name": "Get Video Generation Status1", "type": "n8n-nodes-base.httpRequest", "position": [680, 600], "parameters": {"url": "https://runwayml.p.rapidapi.com/status", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "uuid", "value": "={{ $json.uuid }}"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "runwayml.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": "\"your-rapid-api-key\""}]}}, "typeVersion": 4.2}, {"id": "520eb9eb-4207-4c3e-b741-8e1176904e33", "name": "Confirm Generation Status", "type": "n8n-nodes-base.switch", "position": [500, 760], "parameters": {"rules": {"values": [{"outputKey": "completed", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "86af9233-1825-4ce0-a39f-4665a244b0a5", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.status }}", "rightValue": "success"}]}, "renameOutput": true}, {"outputKey": "pending", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3f431dfc-ce04-40f0-83b8-93e7df665340", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.status }}", "rightValue": "success"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "809bacea-b6ec-4b5d-876e-34591ed0138a", "name": "1 minute3", "type": "n8n-nodes-base.wait", "position": [500, 600], "webhookId": "912371bb-33b4-4879-81b2-8674cd25be9c", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "40497b66-2d13-439e-a7c5-a7fcd52f1e3b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [200, 500], "parameters": {"color": 6, "width": 740, "height": 520, "content": "## Image to Video: Using Runway (Rapid API)"}, "typeVersion": 1}, {"id": "d9e6ab4c-2ba9-4e83-9564-d5389ed5850f", "name": "1 minute2", "type": "n8n-nodes-base.wait", "position": [-560, 800], "webhookId": "51231920-f912-4593-af70-52c5f2fa2e14", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "259f3a3d-adba-4320-82c6-8b16941ef71d", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-860, 740], "parameters": {"color": 5, "width": 740, "height": 280, "content": "## Image to Video: Using Runway (official api)"}, "typeVersion": 1}, {"id": "449f8334-99e8-4f95-8d61-ff844e600d28", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [140, 140], "parameters": {"color": 3, "width": 860, "height": 940, "content": "## Using Rapid API Endpoints"}, "typeVersion": 1}, {"id": "7063bd58-019f-46c4-a863-0057ba139a6f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-920, 140], "parameters": {"color": 4, "width": 860, "height": 940, "content": "## Using official APIs"}, "typeVersion": 1}, {"id": "fed6f706-cfa2-4f8e-bace-d6376394ffaf", "name": "Download Video", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [-380, 800], "parameters": {"url": "=https://api.dev.runwayml.com/v1/tasks/{{ $json.id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}, {"name": "Authorization", "value": "\"your-bearer-key\""}]}}, "typeVersion": 4.2}, {"id": "69d02b6f-0ada-4997-8b74-e79cc16d35ad", "name": "generate image (flux)", "type": "n8n-nodes-base.httpRequest", "position": [-560, 280], "parameters": {"url": "https://api.us1.bfl.ai/v1/flux-pro-1.1", "method": "POST", "options": {}, "jsonBody": "={\n  \"prompt\" : \"your_text_to_image_prompt\",\n  \"width\": 768,\n  \"height\": 1024,\n  \"prompt_upsampling\": false,\n  \"seed\": 42,\n  \"safety_tolerance\": 2,\n  \"output_format\": \"jpeg\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-key", "value": "\"your-key\""}]}}, "typeVersion": 4.2}, {"id": "6dc28e39-fbc1-46c6-a344-c1c4af8b8f96", "name": "generate image (flux-rapid-api)", "type": "n8n-nodes-base.httpRequest", "position": [300, 280], "parameters": {"url": "https://ai-text-to-image-generator-flux-free-api.p.rapidapi.com/aaaaaaaaaaaaaaaaaiimagegenerator/quick.php", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "=\"your_text_to_image_prompt\""}, {"name": "style_id", "value": "1"}, {"name": "size", "value": "1-1"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "ai-text-to-image-generator-flux-free-api.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": "\"your-rapid-api-key\""}]}}, "typeVersion": 4.2}, {"id": "b1c4710e-d99a-42bc-8de2-477b2fca84da", "name": "image to video (runway-rapid-api)", "type": "n8n-nodes-base.httpRequest", "position": [300, 600], "parameters": {"url": "https://runwayml.p.rapidapi.com/generate/imageDescription", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "text_prompt", "value": "=\"your_image_to_video_prompt\""}, {"name": "img_prompt", "value": "={{ $json.kraked_url }}"}, {"name": "model", "value": "gen3"}, {"name": "image_as_end_frame", "value": "false"}, {"name": "flip", "value": "true"}, {"name": "motion", "value": "5"}, {"name": "seed", "value": "0"}, {"name": "time", "value": "10"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "runwayml.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": "\"your-rapid-api-key\""}]}}, "typeVersion": 4.2}, {"id": "82ed20f3-15ba-4369-80c0-1b5913ddccfa", "name": "image to video (runway)", "type": "n8n-nodes-base.httpRequest", "position": [-760, 800], "parameters": {"url": "https://api.dev.runwayml.com/v1/image_to_video", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "promptImage", "value": "={{ $json.kraked_url }}"}, {"name": "model", "value": "gen3a_turbo"}, {"name": "ratio", "value": "768:1280"}, {"name": "duration", "value": "10"}, {"name": "promptText", "value": "=\"your_image_to_video_prompt\""}]}, "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}, {"name": "Authorization", "value": "\"Your-bearer-key\""}]}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "716e405d-b8bc-4c61-a23c-427bedf7ea7f", "connections": {"Wait": {"main": [[{"node": "get_image_url", "type": "main", "index": 0}]]}, "1 minute2": {"main": [[{"node": "Download Video", "type": "main", "index": 0}]]}, "1 minute3": {"main": [[{"node": "Get Video Generation Status1", "type": "main", "index": 0}]]}, "get_image": {"main": [[{"node": "upload to kraken", "type": "main", "index": 0}]]}, "Get Image3": {"main": [[{"node": "upload to kraken1", "type": "main", "index": 0}]]}, "get_image_url": {"main": [[{"node": "get_image", "type": "main", "index": 0}]]}, "Download Video": {"main": [[], [{"node": "1 minute2", "type": "main", "index": 0}]]}, "upload to kraken": {"main": [[{"node": "image to video (runway)", "type": "main", "index": 0}]]}, "upload to kraken1": {"main": [[{"node": "image to video (runway-rapid-api)", "type": "main", "index": 0}]]}, "generate image (flux)": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "image to video (runway)": {"main": [[{"node": "1 minute2", "type": "main", "index": 0}]]}, "Confirm Generation Status": {"main": [[], [{"node": "1 minute3", "type": "main", "index": 0}]]}, "Get Video Generation Status1": {"main": [[{"node": "Confirm Generation Status", "type": "main", "index": 0}]]}, "generate image (flux-rapid-api)": {"main": [[{"node": "Get Image3", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "generate image (flux)", "type": "main", "index": 0}]]}, "image to video (runway-rapid-api)": {"main": [[{"node": "1 minute3", "type": "main", "index": 0}]]}}}