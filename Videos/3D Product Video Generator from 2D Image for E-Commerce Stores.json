{"id": "Njfc4Qsoi6p35Bfj", "meta": {"instanceId": "fcb622cbd5234c565b9b78babeaf0ea2455df9d2aad0d1adff7c1ef99583e685", "templateCredsSetupCompleted": true}, "name": "3D Product Video", "tags": [], "nodes": [{"id": "a27f6012-7a92-436d-8c28-fa0ffca700f6", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [1120, 180], "webhookId": "4d338cdb-1534-43be-97dc-070299cd6278", "parameters": {"amount": 40}, "typeVersion": 1.1}, {"id": "78761c5e-dfe1-4aab-bd0f-f2812d6c0b4e", "name": "On Form Submission", "type": "n8n-nodes-base.formTrigger", "position": [-1080, 80], "webhookId": "7980414e-c599-45fe-98a7-4fd1d121154a", "parameters": {"options": {}, "formTitle": "Shopify 3D Product Video", "formFields": {"values": [{"fieldType": "file", "fieldLabel": "Product Photo", "multipleFiles": false, "requiredField": true}, {"fieldLabel": "Product Title", "placeholder": "Product Title", "requiredField": true}]}, "formDescription": "Give us a product photo, title and we'll get back to you with professional marketing 3D video. "}, "typeVersion": 2.2}, {"id": "1506bc41-d914-4031-bb47-7822c33bddcb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1080, -160], "parameters": {"width": 880, "height": 140, "content": "## 3D Product Video\n\nSample Google Sheet\n- https://docs.google.com/spreadsheets/d/18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw/edit?gid=0#gid=0"}, "typeVersion": 1}, {"id": "d615090d-236d-444a-add9-f9a429019088", "name": "Give Access to folder", "type": "n8n-nodes-base.googleDrive", "position": [-420, 80], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $('Create Folder').item.json.id }}"}, "options": {}, "operation": "share", "permissionsUi": {"permissionsValues": {"role": "reader", "type": "anyone"}}}, "credentials": {"googleDriveOAuth2Api": {"id": "aqCousOTolMFE3cy", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "********-71c0-4900-9d00-d017213fe856", "name": "Get Image File", "type": "n8n-nodes-base.code", "position": [-200, 80], "parameters": {"jsCode": "let results = [];\nconst items = $(\"On Form Submission\").all()\n\nfor (item of items) {\n    for (key of Object.keys(item.binary)) {\n        results.push({\n            json: {\n                fileName: item.binary[key].fileName\n            },\n            binary: {\n                data: item.binary[key],\n            }\n        });\n    }\n}\n\nreturn results;"}, "executeOnce": true, "retryOnFail": true, "typeVersion": 2, "alwaysOutputData": true}, {"id": "ee55f4d4-475f-4220-843a-4ad8b16bbad8", "name": "Upload Remove BG image", "type": "n8n-nodes-base.googleDrive", "position": [240, 180], "parameters": {"name": "=(no-bg){{ $('Create Folder').item.json.name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('Create Folder').item.json.id }}"}, "inputDataFieldName": "=no-bg.png"}, "credentials": {"googleDriveOAuth2Api": {"id": "aqCousOTolMFE3cy", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "********-9abe-4a92-9f82-2486a8b58eab", "name": "Extract Image ID from URL", "type": "n8n-nodes-base.code", "position": [680, 180], "parameters": {"jsCode": "const url = $input.first().json['BG Remove Image']\n\n// Extract the ID using a regular expression\nconst match = url.match(/\\/d\\/([^/]+)/);\n\nreturn {\n  json: {\n    fileId: match ? match[1] : null,\n  },\n};"}, "typeVersion": 2}, {"id": "88d1f349-959a-498f-8f2f-ab45d3d9b25f", "name": "Upload Video", "type": "n8n-nodes-base.googleDrive", "position": [2220, 180], "parameters": {"name": "=Video-file", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('Create Folder').item.json.id }}"}, "inputDataFieldName": "Video-file"}, "credentials": {"googleDriveOAuth2Api": {"id": "aqCousOTolMFE3cy", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "bf26fc49-f91c-40f1-83d3-d2330f242f56", "name": "Create Folder", "type": "n8n-nodes-base.googleDrive", "position": [-640, 80], "parameters": {"name": "={{ $json.slug }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultUrl": "https://drive.google.com/drive", "cachedResultName": "/ (Root folder)"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "aqCousOTolMFE3cy", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "4eadd7af-a13d-40a8-b886-71c798acc547", "name": "Create Slug", "type": "n8n-nodes-base.code", "position": [-860, 80], "parameters": {"jsCode": "const title = $json[\"Product Title\"] || \"default\";\nconst slug = title.toLowerCase().replace(/\\s+/g, '-') + '-' + Math.random().toString(36).substring(2, 8);\nreturn { slug };\n"}, "typeVersion": 2}, {"id": "e53cab81-e81c-412a-a8c1-bf889e6ee063", "name": "Create Video", "type": "n8n-nodes-base.httpRequest", "position": [900, 180], "parameters": {"url": "https://queue.fal.run/fal-ai/kling-video/v1/standard/image-to-video", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "A product placed on a reflective surface slowly rotates 360 degrees with dramatic studio lighting, soft shadows, and a smooth camera pan. The background is clean, minimal, and cinematic, highlighting the product’s details and elegance"}, {"name": "image_url", "value": "=https://drive.usercontent.google.com/download?id={{ $json.fileId }}&export=view&authuser=0"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Key api_key"}]}}, "typeVersion": 4.2}, {"id": "037d6b30-34fe-4fd7-918a-c7d3025b5bfe", "name": "Check Video Status", "type": "n8n-nodes-base.httpRequest", "position": [1340, 105], "parameters": {"url": "={{ $json.status_url }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Key api_key"}]}}, "typeVersion": 4.2}, {"id": "2a74bdb2-efcb-4ede-ba3d-e0716a719c96", "name": "Get Video Link", "type": "n8n-nodes-base.httpRequest", "position": [1780, 180], "parameters": {"url": "={{ $json.response_url }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Key api_key"}]}}, "typeVersion": 4.2}, {"id": "fbf59ebf-991e-4b77-8987-77716c06af0f", "name": "Remove Image Background", "type": "n8n-nodes-base.httpRequest", "position": [20, 180], "parameters": {"url": "https://api.remove.bg/v1.0/removebg", "method": "POST", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "no-bg.png"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "size", "value": "auto"}, {"name": "image_file", "parameterType": "formBinaryData", "inputDataFieldName": "=data"}]}, "headerParameters": {"parameters": [{"name": "X-API-Key", "value": "api_key"}]}}, "executeOnce": false, "typeVersion": 4.2}, {"id": "4c10c26e-3caa-45f0-bf12-9779cd82d87c", "name": "Upload Original Image", "type": "n8n-nodes-base.googleDrive", "position": [20, -20], "parameters": {"name": "={{ $('Create Folder').item.json.name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('Create Folder').item.json.id }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "aqCousOTolMFE3cy", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "1b9c83e7-5d77-4477-9518-d72a6c2f624a", "name": "Insert New Product", "type": "n8n-nodes-base.googleSheets", "position": [240, -20], "parameters": {"columns": {"value": {"Slug": "={{ $('Create Slug').item.json.slug }}", "Product Title": "={{ $('On Form Submission').item.json['Product Title'] }}", "Original Image": "={{ $json.webViewLink }}"}, "schema": [{"id": "Original Image", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Original Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BG Remove Image", "type": "string", "display": true, "removed": true, "required": false, "displayName": "BG Remove Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Product Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video Link", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Video Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Slug", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Slug", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw/edit?usp=drivesdk", "cachedResultName": "3D Product Video"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9JjVrrcwf7D4bsa6", "name": "Google Credentials"}}, "typeVersion": 4.5}, {"id": "b3492513-e591-4106-a806-9fb1c43f513b", "name": "Update Remove BG URL", "type": "n8n-nodes-base.googleSheets", "position": [460, 180], "parameters": {"columns": {"value": {"Slug": "={{ $('Create Folder').item.json.name }}", "BG Remove Image": "={{ $json.webViewLink }}"}, "schema": [{"id": "Original Image", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Original Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BG Remove Image", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BG Remove Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video Link", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Video Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Slug", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Slug", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Slug"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw/edit?usp=drivesdk", "cachedResultName": "3D Product Video"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9JjVrrcwf7D4bsa6", "name": "Google Credentials"}}, "typeVersion": 4.5}, {"id": "cbc141e9-7cf5-4c57-a4b1-d719182cb997", "name": "Is In Progress", "type": "n8n-nodes-base.if", "position": [1560, 180], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8ada0859-b74f-4d48-ae95-30c34340e39f", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "IN_PROGRESS"}]}}, "typeVersion": 2.2}, {"id": "eb09040d-f4d9-419e-be9c-361a2ef65531", "name": "Convert to Binary File", "type": "n8n-nodes-base.httpRequest", "position": [2000, 180], "parameters": {"url": "={{ $json.video.url }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "Video-file"}}}}, "typeVersion": 4.2}, {"id": "b2c905dc-2743-4b5d-b850-facecd489166", "name": "Update Video Link", "type": "n8n-nodes-base.googleSheets", "position": [2440, 180], "parameters": {"columns": {"value": {"Slug": "={{ $('Create Folder').item.json.name }}", "Video Link": "={{ $('Get Video Link').item.json.video.url }}"}, "schema": [{"id": "Original Image", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Original Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BG Remove Image", "type": "string", "display": true, "removed": true, "required": false, "displayName": "BG Remove Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video Link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Video Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Slug", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Slug", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Slug"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18k1Gq2X2J3_cbwJ9XyJoysVuhIpWhgc1cmlTKBnB3Yw/edit?usp=drivesdk", "cachedResultName": "3D Product Video"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9JjVrrcwf7D4bsa6", "name": "Google Credentials"}}, "typeVersion": 4.5}, {"id": "7adb552d-939f-42d1-b425-45b88d52aa71", "name": "Send E-Mail Notification", "type": "n8n-nodes-base.gmail", "position": [2660, 180], "webhookId": "346e7266-8fb6-4b86-9b32-2ca6c1258566", "parameters": {"sendTo": "<EMAIL>", "message": "=<p>Hi there! 👋</p>\n\n<p><b>We’re excited to share our latest product video with you! 🎉<b></p>\n\n<p>Watch it here to see the product video you’ll love:<br>\n<a href=\"{{ $('Get Video Link').item.json.video.url }}\">{{ $('Get Video Link').item.json.video.url }}</a></p>\n\n<p>Don’t miss out—this could be just what you’ve been looking for! ✨</p>\n\n<p><b>Thanks for being part of our community! 🙌<b></p>\n", "options": {"appendAttribution": false}, "subject": "Product Video"}, "credentials": {"gmailOAuth2": {"id": "hLh5uexmYpuaLRC3", "name": "Gmail account 2"}}, "typeVersion": 2.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "51abcabc-2833-4f0d-a153-404df659bfb6", "connections": {"Wait": {"main": [[{"node": "Check Video Status", "type": "main", "index": 0}]]}, "Create Slug": {"main": [[{"node": "Create Folder", "type": "main", "index": 0}]]}, "Create Video": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Upload Video": {"main": [[{"node": "Update Video Link", "type": "main", "index": 0}]]}, "Create Folder": {"main": [[{"node": "Give Access to folder", "type": "main", "index": 0}]]}, "Get Image File": {"main": [[{"node": "Upload Original Image", "type": "main", "index": 0}, {"node": "Remove Image Background", "type": "main", "index": 0}]]}, "Get Video Link": {"main": [[{"node": "Convert to Binary File", "type": "main", "index": 0}]]}, "Is In Progress": {"main": [[{"node": "Wait", "type": "main", "index": 0}], [{"node": "Get Video Link", "type": "main", "index": 0}]]}, "Update Video Link": {"main": [[{"node": "Send E-Mail Notification", "type": "main", "index": 0}]]}, "Check Video Status": {"main": [[{"node": "Is In Progress", "type": "main", "index": 0}]]}, "Insert New Product": {"main": [[]]}, "On Form Submission": {"main": [[{"node": "Create Slug", "type": "main", "index": 0}]]}, "Update Remove BG URL": {"main": [[{"node": "Extract Image ID from URL", "type": "main", "index": 0}]]}, "Give Access to folder": {"main": [[{"node": "Get Image File", "type": "main", "index": 0}]]}, "Upload Original Image": {"main": [[{"node": "Insert New Product", "type": "main", "index": 0}]]}, "Convert to Binary File": {"main": [[{"node": "Upload Video", "type": "main", "index": 0}]]}, "Upload Remove BG image": {"main": [[{"node": "Update Remove BG URL", "type": "main", "index": 0}]]}, "Remove Image Background": {"main": [[{"node": "Upload Remove BG image", "type": "main", "index": 0}]]}, "Extract Image ID from URL": {"main": [[{"node": "Create Video", "type": "main", "index": 0}]]}}}