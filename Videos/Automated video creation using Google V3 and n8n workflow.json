{"meta": {"instanceId": "353ac65d120d2007b8a1503e85a96651a9b2cbb94b0334693ebac2129fa6a7ee", "templateCredsSetupCompleted": true}, "nodes": [{"id": "d790575f-b251-4431-b993-b5b23ac98ed8", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-500, 140], "parameters": {}, "typeVersion": 1}, {"id": "ebd26e9d-956e-49d5-9819-f560c443163f", "name": "📊 Fetch Ready Ideas from Sheet", "type": "n8n-nodes-base.googleSheets", "position": [-280, 140], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "ready", "lookupColumn": "Status"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1VHJFdJjqKWT973NK4TVTdzodAiHM4Zzb4tIkK-kqqGc/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1VHJFdJjqKWT973NK4TVTdzodAiHM4Zzb4tIkK-kqqGc", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1VHJFdJjqKWT973NK4TVTdzodAiHM4Zzb4tIkK-kqqGc/edit?usp=drivesdk", "cachedResultName": "VEO3 Video Generation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "uvQG3YEEXtu5NVSj", "name": "Freelance Account"}}, "typeVersion": 4.6}, {"id": "d47052aa-2bd9-4273-99f0-2d3b8cb593f9", "name": "🤖 VEO3 Prompt Generator (GPT-4.1)", "type": "@n8n/n8n-nodes-langchain.agent", "position": [100, 120], "parameters": {"text": "=User Idea: {{ $json.Idea }}", "options": {"systemMessage": "=SYSTEM PROMPT TO GENERATE PROMPT FOR VEO3\n\nThe key to crafting a strong prompt in Veo 3 is understanding what truly matters in a video. To ensure you include all the essential details, we recommend starting by breaking your prompt down into clear categories:\n\nScene description: Overall description of the scene you want to create — what’s happening, who’s involved, and the general atmosphere.\nVisual style: Defines the overall look and feel of the video — for example, cinematic, realistic, animated, stylized, or surreal.\nCamera movement: Describes how the camera should move during the scene — for instance, slow pan, static shot, tracking shot, or aerial zoom. Avoid using slang terms (eg ‘robo-arm’ or ‘lens crack’). Be clear and descriptive.\nMain subject: The primary person, character, or object that should be the focus of the video. Clearly describe who is doing what in the scene.\nBackground setting: The specific location or environment where the scene takes place — such as a city street at night, a forest during sunrise, or a futuristic lab., traditional village, etc.\nLighting/mood: Describes the type of lighting and the emotional tone you want — for example, soft natural light with a warm tone, or harsh lighting for a dramatic or tense mood.\nAudio cue (optional): Any specific sound or music that should be heard during the scene — such as a song starting when the character dances, or ambient sounds like rain, footsteps, or birdsong.\nColor palette: The dominant colors or tones used in the video — such as bold and bright, pastel, muted earth tones, or monochrome. This influences the emotional and visual impact.\nDialog / Background Noise (optional): If characters are speaking, specify exactly what they should say and when. You can also include environmental sounds like crowds, wind, or traffic.\nSubtitles and Language : Clearly state whether you want subtitles included or not (e.g., “Do not include subtitles” or “Include subtitles in English”). Note that mentioning a language can influence cultural context (clothing, signs, architecture) even if no spoken language is rendered. To imply language or culture, you can write things like: dialogue in Spanish, with English subtitles, on-screen text in Japanese, Arabic calligraphy visible on walls, Korean street signs in the background, etc.\nAdditional Planning Notes\nUsing ” or ” or ( ) or [ ] for the dialogue may generate subtitles – avoid these if you don’t want them.\nUse natural language\nUse long prompts. The more information and direction you can give, the better your output.\nUse a solid prompting structure, like the one outlined above.\nWriting Your Prompt\nOnce you’ve outlined the key information you want to include using the above structure (or something similar), you need to write your prompt. You can write this prompt yourself, or use a tool like Chat GPT to transform your dot point plan into a fully fledged prompt.\n\nCheck out the examples below to see how your prompt should look before hitting ‘Generate’!\n\nExample\nPrompt Plan:\n\nScene description: A glamorous woman with an afro hairstyle and gold jewellery shares a pool at night with a calm alligator, surrounded by dimly lit classical decor and blue ceramic vases.\nVisual style: Cinematic\nCamera movement: Slow zoom-in on the woman’s face from water level\nMain subject: Elegant woman in the pool next to an alligator, making intense eye contact with the camera\nBackground setting: Luxurious outdoor pool area with classical statues and large ceramic vases\nLighting/mood: Moody and soft, with candlelight reflections and subtle fill light on the face\nAudio cue: Slow ambient electronic music with occasional water ripple sounds\nColor palette: Deep slate gray, ocean blue, and warm gold tones\nDialog: “I’m not afraid of anything… except Sundays without drama.\nSubtitles: Off\nComplete Prompt:\n\nA cinematic scene set at night: a glamorous woman with an afro hairstyle and gold jewellery shares a luxurious outdoor pool with a calm alligator. The pool area is surrounded by classical statues and large blue ceramic vases, all dimly lit. The camera starts at water level and slowly zooms in on the woman’s face as she makes intense eye contact with the camera. The lighting is moody and soft, with candlelight reflections on the water and a subtle fill light on her face. The audio features slow ambient electronic music, with occasional water ripple sounds. The color palette includes deep slate gray, ocean blue, and warm gold tones. The woman says, “I’m not afraid of anything… except Sundays without drama.” No subtitles.\n\n\nNote:\n- At the end just provide me single prompt which contain every details just like \"Complete Prompt above\"\n- Make sure no double qoute is there in the response"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 2}, {"id": "45c5196e-37ad-4db2-aa2f-50b79d7d4db2", "name": "🧠 OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [80, 320], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "69W0MY1rH5ycGoOb", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "fb2f6780-f6b4-43fb-93f3-199c33bbdc92", "name": "📝 Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [320, 280], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"prompt\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "bbc4704f-5f9d-4c05-a5b2-1da96c5dd8e7", "name": "🎬 Create Video with VEO3 (Fal AI)", "type": "n8n-nodes-base.httpRequest", "position": [740, 120], "parameters": {"url": "https://queue.fal.run/fal-ai/veo3", "body": "={\n\"prompt\": \"{{ $json.output.prompt }}\"\n}", "method": "POST", "options": {"batching": {"batch": {"batchSize": 1, "batchInterval": 2000}}}, "sendBody": true, "contentType": "raw", "authentication": "genericCredentialType", "rawContentType": "application/json", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "YC8HR4kSfpRBSFrp", "name": "Fal AI Accounts"}}, "typeVersion": 4.2}, {"id": "afa88152-0a60-4fb7-b765-7f737df81636", "name": "🔍 Check Video Generation Status", "type": "n8n-nodes-base.httpRequest", "position": [960, 120], "parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $json.request_id }}/status", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "YC8HR4kSfpRBSFrp", "name": "Fal AI Accounts"}}, "typeVersion": 4.2}, {"id": "93fda276-8061-4a20-a302-0aecc929a2ec", "name": "✅ Is Video Generation Complete?", "type": "n8n-nodes-base.if", "position": [1180, 60], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "571d2dfe-8a0c-416a-8780-3f47b17286b3", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "COMPLETED"}]}}, "typeVersion": 2.2}, {"id": "c5803650-1462-41e2-bbf2-2b9f1a26bf98", "name": "⏰ Wait Before Retry", "type": "n8n-nodes-base.wait", "position": [1400, 220], "webhookId": "0f17aa86-f5f6-424c-b302-91bc70001367", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "2ebccd28-fba3-4bb0-9e1a-386e4b71fd05", "name": "📥 Download Generated Video", "type": "n8n-nodes-base.httpRequest", "position": [1400, 0], "parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $json.request_id }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "YC8HR4kSfpRBSFrp", "name": "Fal AI Accounts"}}, "typeVersion": 4.2}, {"id": "3ab5da68-d834-41f9-aef3-9de8fa81b0b9", "name": "📊 Update Sheet with Video URL", "type": "n8n-nodes-base.googleSheets", "position": [1860, 140], "parameters": {"columns": {"value": {"Status": "completed", "Video URL": "={{ $json.video.url }}", "row_number": "={{ $('📊 Fetch Ready Ideas from Sheet').item.json.row_number }}"}, "schema": [{"id": "Idea", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Idea", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "string", "display": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video URL", "type": "string", "display": true, "required": false, "displayName": "Video URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1VHJFdJjqKWT973NK4TVTdzodAiHM4Zzb4tIkK-kqqGc/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1VHJFdJjqKWT973NK4TVTdzodAiHM4Zzb4tIkK-kqqGc", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1VHJFdJjqKWT973NK4TVTdzodAiHM4Zzb4tIkK-kqqGc/edit?usp=drivesdk", "cachedResultName": "VEO3 Video Generation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "uvQG3YEEXtu5NVSj", "name": "Freelance Account"}}, "typeVersion": 4.6}, {"id": "b5bc4902-7953-4f5e-8ad1-65218fe5bb45", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [420, -660], "parameters": {"color": 6, "width": 500, "height": 320, "content": "## 🎬 VEO3 AI Video Generation Pipeline\n**Automated video creation from ideas to final output**\n\n### 🔄 Workflow Steps:\n1. **Data Input** → Fetch video ideas from Google Sheets\n2. **Prompt Engineering** → AI generates optimized VEO3 prompts  \n3. **Video Creation** → Submit to VEO3 via Fal AI platform\n4. **Status Monitoring** → Poll until video generation completes\n5. **Result Storage** → Save video URL back to Google Sheets\n\n### 📝 Useful Docs\nVeo 3 Model Link - [Fal.ai](https://fal.ai/models/fal-ai/veo3)\n\n⚡ **Auto-retry system included for reliable video generation**"}, "typeVersion": 1}, {"id": "e47be290-c70d-4972-8286-67267eca3302", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-640, -100], "parameters": {"width": 520, "height": 480, "content": "## 📊 Data Input & Trigger System\n**Fetches ready video ideas from Google Sheets**\n\n### 🔧 Configuration Required:\n- Set correct Google Sheets document ID\n- Filter for \"Status = ready\" rows\n- Ensure \"Idea\" column contains video concepts"}, "typeVersion": 1}, {"id": "ece8e050-bc22-4d6c-ac88-178b60f68fe6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-80, -240], "parameters": {"color": 2, "width": 700, "height": 740, "content": "## 🤖 AI-Powered Prompt Engineering\n**Transforms simple ideas into detailed VEO3 prompts**\n\n### 🎯 Process:\n1. **Input:** Basic video idea from sheet\n2. **AI Analysis:** GPT-4 enhances with VEO3 best practices\n3. **Output:** Structured prompt with all required elements\n\n### 📝 Prompt Elements Generated:\n- Scene description & visual style\n- Camera movements & lighting\n- Main subjects & background settings\n- Color palette & audio cues\n- Subtitle preferences"}, "typeVersion": 1}, {"id": "13f89c59-6c96-4d3f-a83a-55901ecbca1a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [680, -240], "parameters": {"color": 3, "width": 980, "height": 740, "content": "## 🎬 VEO3 Video Generation & Monitoring\n**Handles video creation with smart retry system**\n\n### ⚙️ Generation Settings:\n- **Aspect Ratio:** 9:16 (Mobile optimized)\n- **Duration:** 5 seconds\n- **Audio:** Generated automatically\n- **Prompt Enhancement:** Enabled\n\n### 🔄 Smart Monitoring:\n- Checks status every 30 seconds\n- Auto-retries until \"COMPLETED\"\n- Downloads final video when ready"}, "typeVersion": 1}, {"id": "9c9e1dc4-afc5-4b90-8a22-acd2b68e86f8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1720, -240], "parameters": {"color": 4, "width": 400, "height": 740, "content": "## 📥 Results Processing & Storage\n**Saves generated video URLs back to Google Sheets**\n\n### 💾 What Gets Saved:\n- Final video download URL\n- Updates same row where idea was fetched\n- Status automatically changes to \"completed\"\n\n### ✅ Success Indicators:\n- Video URL populated in sheet\n- File accessible via direct link\n- Ready for download/sharing"}, "typeVersion": 1}, {"id": "360fb8b0-7a99-4bb3-8b0e-44c58fd15790", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-780, -900], "parameters": {"color": 6, "width": 520, "height": 680, "content": "## 👨‍💻 Creator Information\n\n**Created by:** <PERSON><PERSON><PERSON>  \n🔗 **LinkedIn:** [Connect with me](https://www.linkedin.com/in/lakshit-ukani/)\n🔗 **Youtube:** [Subscribe to my Channel](https://www.youtube.com/@lakshit-ukani?sub_confirmation=1)\n\n### Youtube Tutorial\n[![Thumbnail](https://i.ytimg.com/vi/kMRTaVRPwTw/hqdefault.jpg?s…BACGAY4AUAB&rs=AOn4CLCW0jAAbexQ34U3892BoCxYgrpDTw)](https://www.youtube.com/watch?v=kMRTaVRPwTw)\n\n### 🤝 Community & Support\n💬 **Join Community:** [Skool Community Link](https://www.skool.com/ai-automation-club-7843)   \n📚 **Documentation:** [VEO3 API Guide](https://docs.fal.ai/models/kling-video)  \n\n### 📞 Need Help?\n- **Quick Questions:** Comment on the YouTube video\n- **Workflow Issues:** Post in our community  \n- **Business Inquiries:** Connect via LinkedIn"}, "typeVersion": 1}], "pinData": {"📥 Download Generated Video": [{"video": {"url": "https://v3.fal.media/files/elephant/iFIb2SSbBVpakelQJR8NF_output.mp4", "file_name": "output.mp4", "file_size": 7960317, "content_type": "video/mp4"}}], "🔍 Check Video Generation Status": [{"logs": null, "status": "COMPLETED", "metrics": {"inference_time": 146.31277418136597}, "cancel_url": "https://queue.fal.run/fal-ai/veo3/requests/00900707-dbe9-4fa9-ad55-3297bfc4362b/cancel", "request_id": "00900707-dbe9-4fa9-ad55-3297bfc4362b", "status_url": "https://queue.fal.run/fal-ai/veo3/requests/00900707-dbe9-4fa9-ad55-3297bfc4362b/status", "response_url": "https://queue.fal.run/fal-ai/veo3/requests/00900707-dbe9-4fa9-ad55-3297bfc4362b"}], "🎬 Create Video with VEO3 (Fal AI)": [{"logs": null, "status": "IN_QUEUE", "metrics": {}, "cancel_url": "https://queue.fal.run/fal-ai/veo3/requests/00900707-dbe9-4fa9-ad55-3297bfc4362b/cancel", "request_id": "00900707-dbe9-4fa9-ad55-3297bfc4362b", "status_url": "https://queue.fal.run/fal-ai/veo3/requests/00900707-dbe9-4fa9-ad55-3297bfc4362b/status", "response_url": "https://queue.fal.run/fal-ai/veo3/requests/00900707-dbe9-4fa9-ad55-3297bfc4362b", "queue_position": 0}], "🤖 VEO3 Prompt Generator (GPT-4.1)": [{"output": {"prompt": "A cozy and enchanting scene of a friendly yeti giving a house tour inside his winter cabin. The visual style is whimsical and slightly animated with soft textures to evoke warmth and charm. The camera operates with gentle tracking shots as it follows the yeti moving through different rooms, highlighting the various quirky and snowy decor. The main subject is the yeti, a large, fluffy white creature with expressive eyes and a warm smile, gesturing enthusiastically as he shows his house. The background setting is a rustic, snow-covered wooden cabin filled with winter-themed decorations, plush furniture, and icicle-shaped lights. The lighting is soft and warm with a golden hue, creating a welcoming and cheerful atmosphere. Ambient sounds include crackling of a fireplace, soft wind blowing outside, and light, playful background music to enhance the friendly mood. The color palette features cool whites and blues mixed with warm wooden browns and soft yellows. The yeti occasionally speaks in a gentle, friendly tone, saying things like I hope you like it here and This is my favorite room to relax. Subtitles are not included."}}]}, "connections": {"⏰ Wait Before Retry": {"main": [[{"node": "🔍 Check Video Generation Status", "type": "main", "index": 0}]]}, "🧠 OpenAI Chat Model": {"ai_languageModel": [[{"node": "🤖 VEO3 Prompt Generator (GPT-4.1)", "type": "ai_languageModel", "index": 0}]]}, "📝 Structured Output Parser": {"ai_outputParser": [[{"node": "🤖 VEO3 Prompt Generator (GPT-4.1)", "type": "ai_outputParser", "index": 0}]]}, "📥 Download Generated Video": {"main": [[{"node": "📊 Update Sheet with Video URL", "type": "main", "index": 0}]]}, "✅ Is Video Generation Complete?": {"main": [[{"node": "📥 Download Generated Video", "type": "main", "index": 0}], [{"node": "⏰ Wait Before Retry", "type": "main", "index": 0}]]}, "📊 Fetch Ready Ideas from Sheet": {"main": [[{"node": "🤖 VEO3 Prompt Generator (GPT-4.1)", "type": "main", "index": 0}]]}, "🔍 Check Video Generation Status": {"main": [[{"node": "✅ Is Video Generation Complete?", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "📊 Fetch Ready Ideas from Sheet", "type": "main", "index": 0}]]}, "🎬 Create Video with VEO3 (Fal AI)": {"main": [[{"node": "🔍 Check Video Generation Status", "type": "main", "index": 0}]]}, "🤖 VEO3 Prompt Generator (GPT-4.1)": {"main": [[{"node": "🎬 Create Video with VEO3 (Fal AI)", "type": "main", "index": 0}]]}}}