{"id": "tAqp67CRi6nbgHBL", "meta": {"instanceId": "143d2ab55c8bffb06f8b9c7ad30335764fdc48bbbacecbe2218dadb998a32213", "templateCredsSetupCompleted": true}, "name": "TikTok Posts Automation Agent V3.1", "tags": [], "nodes": [{"id": "ab8c54f0-9046-4160-8d05-059df9078e68", "name": "Audio Transcription", "type": "n8n-nodes-base.httpRequest", "position": [1560, 1780], "parameters": {"url": "https://api.openai.com/v1/audio/transcriptions", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "timestamp_granularities[]", "value": "word"}, {"name": "model", "value": "whisper-1"}, {"name": "response_format", "value": "verbose_json"}, {"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data-audio"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "93YLMhb9yAza1oWn", "name": "OpenAi account 2"}}, "typeVersion": 4.2}, {"id": "c613277c-84b2-4e0a-ad77-29178e4dbeef", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [380, 1840], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "credentials": {"googlePalmApi": {"id": "f334eoOsgoLWbi59", "name": "Google Gemini(PaLM) Api account 2"}}, "typeVersion": 1}, {"id": "f07fbfa9-25b2-44ce-aa89-3e313ad9bca3", "name": "Split Out1", "type": "n8n-nodes-base.splitOut", "position": [740, 1660], "parameters": {"options": {}, "fieldToSplitOut": "output"}, "typeVersion": 1}, {"id": "e2a8243a-2db2-4578-9e90-67d1cc5bde33", "name": "Submit Job", "type": "n8n-nodes-base.httpRequest", "position": [40, 1160], "parameters": {"url": "https://api.assemblyai.com/v2/transcript", "method": "POST", "options": {}, "jsonBody": "={\n  \"audio_url\": \"{{ $json.upload_url }}\"\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "o1HZCtBTQIiMxt5v", "name": "Assembly"}}, "typeVersion": 4.2}, {"id": "2a4ecb88-6b3c-4565-9f92-e6c5febde2fa", "name": "Check Status", "type": "n8n-nodes-base.httpRequest", "position": [460, 1160], "parameters": {"url": "=https://api.assemblyai.com/v2/transcript/{{ $node[\"Submit Job\"].json[\"id\"] }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "o1HZCtBTQIiMxt5v", "name": "Assembly"}}, "typeVersion": 4.2}, {"id": "ceebfea6-6068-46e4-a845-cf0fe209dd0a", "name": "If", "type": "n8n-nodes-base.if", "position": [700, 1160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0e8296de-3d44-47ee-ab04-f5d9e4e00daa", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "completed"}]}}, "typeVersion": 2.2}, {"id": "9797aeb0-470d-45d6-949b-7dc988c3e158", "name": "Extract Results", "type": "n8n-nodes-base.set", "position": [-200, 1660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e5b4b58c-20d7-4c45-898d-4e6518f2fba6", "name": "transcription_text", "type": "string", "value": "={{ $json.text }}"}, {"id": "3e064366-ad18-42d5-bdd9-9420e95675f6", "name": "word_details", "type": "array", "value": "={{ $json.words }}"}]}}, "typeVersion": 3.4, "alwaysOutputData": false}, {"id": "6185ddd8-3f34-4ba1-b6a0-e1cdc530ae48", "name": "Wait1", "type": "n8n-nodes-base.wait", "position": [2340, 1780], "webhookId": "3648a845-4cc5-4d4a-a820-38a4f1906086", "parameters": {"unit": "minutes"}, "typeVersion": 1.1}, {"id": "0502af5a-7950-42f2-9541-8eab1419e4d4", "name": "If1", "type": "n8n-nodes-base.if", "position": [2800, 1780], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "da50dbe5-0d78-4315-a9c4-29a36a34bff5", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "done"}]}}, "typeVersion": 2.2}, {"id": "a86a8f9f-45a9-424f-945f-49c6926114c2", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [3260, 1920], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-001"}, "credentials": {"googlePalmApi": {"id": "f334eoOsgoLWbi59", "name": "Google Gemini(PaLM) Api account 2"}}, "typeVersion": 1}, {"id": "e146d265-54e9-4335-8477-f7e2915e8071", "name": "Structured Output Parser1", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [3560, 1920], "parameters": {"jsonSchemaExample": "{\n  \"title\": \"Who is the Most POWERFUL Man GLOBALLY? | PBD Podcast #pbdpodcast #power #exposed\"\n}"}, "typeVersion": 1.2}, {"id": "80238aea-6c94-4dc2-b5ca-3f58aad008e3", "name": "Send the YouTube Video Links", "type": "n8n-nodes-base.formTrigger", "position": [-220, 660], "webhookId": "4304503e-1200-4d7f-b062-7629b5ca0207", "parameters": {"options": {}, "formTitle": "Required Links", "formFields": {"values": [{"fieldLabel": "YouTube Video URL", "requiredField": true}, {"fieldLabel": "Copyright Free Background Video", "requiredField": true}]}}, "typeVersion": 2.2}, {"id": "358a7d95-f716-4ab1-96c5-eaa0d8d31253", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-300, 500], "parameters": {"color": 4, "width": 3200, "height": 500, "content": "# Video Downloading and Extraction\n"}, "typeVersion": 1}, {"id": "b592b99b-7fda-400a-871c-d88a719aab04", "name": "Transcribe Podcast Audio", "type": "n8n-nodes-base.httpRequest", "position": [-200, 1160], "parameters": {"url": "https://api.assemblyai.com/v2/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "binaryData", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "inputDataFieldName": "audio"}, "credentials": {"httpHeaderAuth": {"id": "o1HZCtBTQIiMxt5v", "name": "Assembly"}}, "typeVersion": 4.2}, {"id": "afbd7dd9-290a-4923-8af6-bc4b838a3003", "name": "Wait 1", "type": "n8n-nodes-base.wait", "position": [260, 1160], "webhookId": "ae60902e-1252-4bf5-a86c-3e77165ab8cd", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "357795ce-83c4-4ad1-a211-bfcea7cbbc28", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-300, 1020], "parameters": {"color": 5, "width": 1220, "height": 440, "content": "# Process Podcast Audio\n"}, "typeVersion": 1}, {"id": "6d2bd147-2027-41cf-b41d-e8a3b14fb11b", "name": "Structure Transcription", "type": "n8n-nodes-base.code", "position": [60, 1660], "parameters": {"jsCode": "// Access the transcription data from the previous node\nconst transcriptData = $input.first().json.word_details; // Adjust this based on your node's output structure\n\n// Check if the data exists and is an array\nif (!transcriptData || !Array.isArray(transcriptData)) {\n  throw new Error(\"Transcription data is not an array or is undefined. Check the previous node's output.\");\n}\n\n// Map the transcript data to match your previous format\nconst formattedTranscript = transcriptData.map((word, index) => {\n  return {\n    text: word.text,   // Adjust 'text' to match your data's property name (e.g., 'word' or 'text')\n    index: index,      // Position in the array\n    start: word.start, // Start time (adjust units if needed, e.g., / 1000 for seconds)\n    end: word.end      // End time (adjust units if needed)\n  };\n});\n\n// Convert the formatted transcript to a JSON string\nconst transcriptJson = JSON.stringify(formattedTranscript);\n\n// Return the result in n8n’s expected format\nreturn [{ json: { transcriptJson: transcriptJson } }];"}, "typeVersion": 2}, {"id": "2777334e-d860-43d3-8385-6f91a437cb8b", "name": "Podcast Best Moments Extraction", "type": "@n8n/n8n-nodes-langchain.chainLlm", "onError": "continueRegularOutput", "position": [380, 1660], "parameters": {"text": "You are given a transcript array where each entry is an object with the following keys:\n- **text**: a string containing the transcript text.\n- **index**: a number.\n- **start**: the starting second.\n- **end**: the ending second.\n\n**Objective:**  \nExtract and merge highlights from the transcript. Identify segments that have high informational value, emotional impact, or key takeaways. Merge adjacent entries if they form a coherent highlight, and remove filler words while preserving meaning.\n\n**Instructions:**\n1. Analyze the transcript and extract coherent highlight segments.\n2. Merge adjacent entries if they form a coherent highlight.\n3. Remove any unnecessary filler words while retaining the essential meaning.\n4. Ensure that the total duration of all highlight segments is no more than 600 seconds.\n5. Ensure that the duration of each highlight is at least 30 seconds and no more than 120 seconds.\n6. Ensure that the segments do not overlap in time.\n\nFor each highlight, output a JSON object with exactly these keys:\n- **transcript**: the text of the highlight segment.\n- **start_index**: the starting word index (using the original value).\n- **end_index**: the ending word index (using the original value).\n\n**CRUCIAL OUTPUT FORMAT REQUIREMENTS:**\n- **Output ONLY a plain JSON array.** The final output must start with `[` and end with `]` and include nothing else.\n- **Do not include any markdown formatting, triple backticks, wrapper objects (such as an \"output\" key), or any additional text.**\n- **Do not output any extra explanation or commentary.** Your entire response must consist solely of the JSON array.\n\nFor example, the expected output format is:\n[\n  {\n    \"transcript\": \"I like this\",\n    \"start_index\": 10,\n    \"end_index\": 13\n  },\n  {\n    \"transcript\": \"It would be\",\n    \"start_index\": 16,\n    \"end_index\": 19\n  }\n]\n\nRemember: Your final response must be exactly the JSON array as specified—nothing else. No markdown fences, no extra keys, no additional text, only the JSON array.", "messages": {"messageValues": [{"type": "HumanMessagePromptTemplate", "message": "={{ $node[\"Structure Transcription\"].json[\"transcriptJson\"] }}"}]}, "promptType": "define", "hasOutputParser": true}, "retryOnFail": true, "typeVersion": 1.5}, {"id": "a17c20de-7007-4579-b9ae-e838ed40af02", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-300, 1500], "parameters": {"color": 6, "width": 1220, "height": 500, "content": "# Video Highlights Generation\n"}, "typeVersion": 1}, {"id": "a8a57cb7-07c8-44a0-afd2-836bea05170f", "name": "Loop Over Clips", "type": "n8n-nodes-base.splitInBatches", "position": [1240, 1280], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "2ecb63e3-8974-42ba-8e63-7cbdf4fed094", "name": "Structure Transcription 2", "type": "n8n-nodes-base.code", "position": [1820, 1780], "parameters": {"jsCode": "return $(\"Audio Transcription\").all().map(item => {\n  const words = item.json.words\n  const result = [];\n\n  for (let i = 0; i < words.length; i += 3) {\n    const chunk = words.slice(i, i + 3);\n    result.push({\n      words: chunk.map(word => word.word.charAt(0).toUpperCase() + word.word.slice(1)).join(' '),\n      start: chunk[0].start,\n      end: chunk[chunk.length - 1].end,\n    });\n  }\n\n  return {\n    json: {\n      chunks: result,\n    },\n  };\n});"}, "typeVersion": 2}, {"id": "4119112e-5111-454b-8d71-a07f1f18cf75", "name": "Editing Clips", "type": "n8n-nodes-base.httpRequest", "position": [2120, 1780], "parameters": {"url": "https://api.andynocode.com/api/function/video-generation/clip-video", "method": "POST", "options": {}, "jsonBody": "={\n    \"type\": \"clip-video\",\n    \"data\": {\n        \"clip_url\": \"{{ $('If5').item.json['download-url'] }}\",\n\"gameplay_url\": \"{{ $('If6').item.json['download-url'] }}\",\n    \"background_url\": \"{{ $('If5').item.json['download-url'] }}\",\n        \"transcripts\": {{ JSON.stringify($('Structure Transcription 2').item.json.chunks) }}\n    }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "UFktnf9LviG1pVSY", "name": "Andynocode 2"}}, "typeVersion": 4.2, "alwaysOutputData": true}, {"id": "8b352799-4d60-4cca-ba22-7d950e6c5f99", "name": "Clip Ready?", "type": "n8n-nodes-base.httpRequest", "position": [2600, 1780], "parameters": {"url": "=https://api.andynocode.com/api/function/video-generation/progress/{{ $json.data.id }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "UFktnf9LviG1pVSY", "name": "Andynocode 2"}}, "typeVersion": 4.2}, {"id": "ce3775e8-02ac-4a02-93c5-fb58e608d730", "name": "Structure Links", "type": "n8n-nodes-base.set", "position": [3080, 1700], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bf605d69-8ae6-4632-bbd0-d7b1cbb4eed5", "name": "final-video-url", "type": "string", "value": "=https://api.andynocode.com{{ $json.data.url }}"}]}}, "typeVersion": 3.4}, {"id": "c5e1708a-3f02-451c-913a-7f18ae954269", "name": "Generate Title", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3260, 1700], "parameters": {"text": "Analyze the following video transcript and generate a compelling TikTok title (under 60 characters, attention-grabbing) and captions (concise, engaging, highlights key moments/hooks).\nFocus on clarity, emotional appeal, and curiosity. Avoid generic phrases. Title must align with the transcript's core message or most viral moment. Output JSON format only.\n\nExample Output:\n\n```json```\n{\n  \"title\": \"Who is the Most POWERFUL Man GLOBALLY? | PBD Podcast #pbdpodcast #power #exposed\"\n}\n```json```\n   ", "messages": {"messageValues": [{"type": "HumanMessagePromptTemplate", "message": "={{ $('Audio Transcription').last().json.text }}"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "14ed2dd9-2e33-4907-8c65-0fe53cd1be24", "name": "Download Final Clip", "type": "n8n-nodes-base.httpRequest", "position": [3700, 1700], "parameters": {"url": "={{ $('Structure Links').item.json['final-video-url'] }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "final-video"}}}}, "typeVersion": 4.2}, {"id": "2bbd3569-dbb7-4573-aaf9-00c3568f062c", "name": "Post To TikTok", "type": "n8n-nodes-base.httpRequest", "position": [4320, 1900], "parameters": {"url": "https://api.upload-post.com/api/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $json.output.title }}"}, {"name": "user", "value": "your_username"}, {"name": "platform[]", "value": "tiktok"}, {"name": "video", "parameterType": "formBinaryData", "inputDataFieldName": "final-video"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "fHczpmGhBLSKy1HV", "name": "TikTok Upload"}}, "typeVersion": 4.2}, {"id": "bebc8548-7bc0-477d-a397-16ff2dfa64a2", "name": "Posting Interval", "type": "n8n-nodes-base.wait", "position": [4660, 1900], "webhookId": "9eae2dfb-186e-44f9-918f-a5238b53641c", "parameters": {"unit": "minutes"}, "typeVersion": 1.1}, {"id": "4afe7455-d1f3-4db4-984a-ee0487dee2bf", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1480, 1160], "parameters": {"color": 4, "width": 2760, "height": 440, "content": "# Pre-Processing Clips \n"}, "typeVersion": 1}, {"id": "03cc6b38-f881-42db-b129-f4bb6515e542", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1480, 1620], "parameters": {"color": 5, "width": 560, "height": 400, "content": "# Pre-Processing Audio"}, "typeVersion": 1}, {"id": "c3db3b36-0a70-46ab-a010-e23568f0c47b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2060, 1620], "parameters": {"color": 6, "width": 960, "height": 400, "content": "# Clip Editing"}, "typeVersion": 1}, {"id": "a05ba940-1aa3-4f24-85d4-cd920c98b63b", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [3040, 1620], "parameters": {"color": 3, "width": 880, "height": 460, "content": "# Clip Preparation for Posting"}, "typeVersion": 1}, {"id": "d03bd6a3-b7a0-47c5-93f4-f0c874a3775f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [4180, 1760], "parameters": {"width": 760, "height": 300, "content": "# Posting"}, "typeVersion": 1}, {"id": "368065b2-5fe4-4af5-9638-fbbf6a457946", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1120, 1020], "parameters": {"color": 7, "width": 3440, "height": 1120, "content": "# **Clip Generation**"}, "typeVersion": 1}, {"id": "e3473bd8-d915-4ebb-92b6-c5ea0cbf716e", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [560, 1860], "parameters": {"jsonSchemaExample": "[{\n  \"transcript\": \"I like this\",\n  \"start_index\": 10,\n  \"end_index\": 13\n},{\n  \"transcript\": \"It would be\",\n  \"start_index\": 16,\n  \"end_index\": 19\n}]"}, "typeVersion": 1.2}, {"id": "f051d49f-57ba-4939-b0b5-16f8f3d5984f", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-980, 500], "parameters": {"width": 620, "height": 1540, "content": "# **Workflow Setup Step-by-Step Guide**\n\n\n### **Workflow Setup Step-by-Step Guide**\n\n**Initial Setup:**  \n1. Create an [AssemblyAI](https://www.assemblyai.com) account for Free.\n2. Create an account in [Google AI Studio](https://aistudio.google.com) for Free.\n3. Create an [Andynocode](https://www.andynocode.com) account for Free.\n4. Create an [Upload-Post](https://www.upload-post.com) account for Free\n \n\n### **Getting Started:**\n4. Go to your [Assembly AI Account](https://www.assemblyai.com/app/account) and copy your API Key.\n5. Go to the \"Transcribe Podcast Audio\", \"Submit Job\" and \"Check Status\" nodes and set up your Assembly credentials. (Process Podcast Audio section)\n\n***Name: Authorization***\n***Value: your_api_key***\n\n6. Go to the Google Gemini Chat model inside the section \"Video Highlights Generation\" and \"Clip Preparation for Posting\" and connect your credentials.\n\n***Host: https://generativelanguage.googleapis.com***\n***API Key: your_api_key***\n\n7. Go to \"Pre-Processing Audio\" section inside the \"Clip Generation and Posting\" section, and connect your Openai credentials inside the \"Audio Transcription\" node.\n\n***API Key: your_api_key***\n***Organization ID (optional): organization_id***\n***Base URL: https://api.openai.com/v1***\n\n8. Go to \"Clip Editing\" section inside the \"Clip Generation and Posting\" section, and connect your Andynocode credentials inside the \"Editing Clips\" and \"Clip Ready?\" nodes.\n\n***Name: api_key***\n***API Key: your_api_key***\n\n9. Go to \"Posting\" section inside the \"Clip Generation and Posting\" section, and connect your Upload-Post  credentials inside the \"Post To TikTok\" node.\n\n***Name: Authorization***\n***Value: Apikey your_api_key***\n\nFinally, inside the same node, in the body request you have to parse your username. It should look like:\n\n***Parameter Type: Form Data***\n***Name: user***\n***Value: your_username***\n\n7.  **The technology that powers this AI Agent incurs real operating costs to stay online.**\nThat’s why the workflow requires an active membership, priced at only **\\$29 per month**.\n\n **Activate your membership here:** [https://lemolex.gumroad.com/l/ypjdr](https://lemolex.gumroad.com/l/ypjdr)\n\nAfter activating the membership you need to set the nodes with the membership generated key: \n- Go to the membership link to get your key.\n- Copy the key (e.g 6F0E4C97-B72A4E69-A11BF6C4-AF123456) and paste it in the body parameters of \"Download Audio Stage 1\" node:\n*Name: auth-token*\n*Value:  6F0E4C97-B72A4E69-A11BF6C4-AF123456* **(example)**\n\n\n### **How To Use**\n- Find a podcast on YouTube and copy it's URL.\n- Find a cool \"attention grabbing\" video to put in the background (e.g. [Minecraft Parkour No Copyright Gameplay](https://youtu.be/XBIaqOm0RKQ?si=gre39-555aRTVsJ1) / [GTA 5 No Copyright Gameplay](https://youtu.be/z121mUPexGc?si=JGsTOI88tOxXdWYx)) and copy it's URL.\n- Start the workflow, send both URLs and effortlessly transform long-form podcast content into highly engaging, viral-ready TikTok clips\n\n\n**For further guidance, check my video or send me a message on [LinkedIn](https://www.linkedin.com/in/mateofioritorocha/)**"}, "typeVersion": 1}, {"id": "67e3707c-b810-48db-b772-b81eaf3133df", "name": "Download Audio Stage 1", "type": "n8n-nodes-base.httpRequest", "position": [220, 660], "parameters": {"url": "https://lemolex.app.n8n.cloud/webhook/74c67c3f-3883-40c2-b0ee-d67ecd1ff9fd", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "podcast_url", "value": "={{ $('Send the YouTube Video Links').item.json['YouTube Video URL'] }}"}, {"name": "auth-token", "value": "your-token (read setup guide)"}]}, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "016005e5-b534-4dc3-96f7-6cc7089aae87", "name": "Download Audio Stage 2", "type": "n8n-nodes-base.httpRequest", "position": [680, 660], "parameters": {"url": "https://lemolex.app.n8n.cloud/webhook/40a6b1-58ba-4ce6-9e52-55cb0cfebafa", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "job-id", "value": "={{ $('Download Audio Stage 1').item.json.id }}"}]}}, "typeVersion": 4.2}, {"id": "6041f73b-e21b-40f2-ab61-e3907cb8c388", "name": "If4", "type": "n8n-nodes-base.if", "position": [1120, 660], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "17476aa1-49ae-4697-aa41-95f00dee6875", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "finished"}]}}, "typeVersion": 2.2}, {"id": "f678f68f-3960-4406-b774-166142282889", "name": "Wait3", "type": "n8n-nodes-base.wait", "position": [2180, 740], "webhookId": "51ec1118-740b-4b39-8056-a466ef4b0479", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "6443e108-b4c2-4923-8f99-8e410babca3c", "name": "Download Audio Stage 3", "type": "n8n-nodes-base.httpRequest", "position": [2480, 660], "parameters": {"url": "={{ $('If4').item.json['download-url'] }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "audio"}}}}, "typeVersion": 4.2}, {"id": "35d24ce0-f86c-4bb2-8dfe-4d97bf34b632", "name": "Generating Main Clip", "type": "n8n-nodes-base.httpRequest", "position": [1560, 1300], "parameters": {"url": "https://lemolex.app.n8n.cloud/webhook/e18f-e237-4f6e-b5a8-fffda04c4d8f", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "podcast_url", "value": "={{ $('Send the YouTube Video Links').item.json['YouTube Video URL'] }}"}, {"name": "startSeconds", "value": "={{ $json.start_index }}"}, {"name": "endSeconds", "value": "={{ $json.end_index }}"}]}}, "typeVersion": 4.2}, {"id": "bb192c11-6acd-4106-9fad-6cff3b1141b7", "name": "If5", "type": "n8n-nodes-base.if", "position": [2000, 1300], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "17476aa1-49ae-4697-aa41-95f00dee6875", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "finished"}]}}, "typeVersion": 2.2}, {"id": "784b3137-a159-4d05-9560-ab997537ac95", "name": "Wait4", "type": "n8n-nodes-base.wait", "position": [2420, 1400], "webhookId": "d9e3f283-7132-480e-a544-0df0a9a8d937", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "d78f2ea4-876a-460a-96f6-2bad8d1e8911", "name": "Generating Background Clip", "type": "n8n-nodes-base.httpRequest", "position": [2420, 1220], "parameters": {"url": "https://lemolex.app.n8n.cloud/webhook/1d9186-e6c6-49f4-9469-21a77ab3fb37", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "background_url", "value": "={{ $('Send the YouTube Video Links').item.json['Copyright Free Background Video'] }}"}, {"name": "startSeconds", "value": "=3"}, {"name": "endSeconds", "value": "={{ $json.interval }}"}]}}, "typeVersion": 4.2}, {"id": "2fce05f9-7dae-410f-85aa-e91cd6909c6a", "name": "Extracting Main Clip", "type": "n8n-nodes-base.httpRequest", "position": [1780, 1300], "parameters": {"url": "https://lemolex.app.n8n.cloud/webhook/40a7b1-58ba-4ce6-9e52-55cb0cfebafa", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "job-id", "value": "={{ $('Generating Main Clip').item.json.id }}"}]}}, "typeVersion": 4.2}, {"id": "4e281e9f-a8b9-4506-b0a0-8ddf1c8d225d", "name": "Extracting Background Clip", "type": "n8n-nodes-base.httpRequest", "position": [2680, 1240], "parameters": {"url": "https://lemolex.app.n8n.cloud/webhook/40a697b1-58ba-4ce6-9e52-55cb0cfebafa", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "job-id", "value": "={{ $('Generating Background Clip').item.json.id }}"}]}}, "typeVersion": 4.2}, {"id": "00e73d51-4814-489b-9f79-488519710627", "name": "If6", "type": "n8n-nodes-base.if", "position": [2880, 1280], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "17476aa1-49ae-4697-aa41-95f00dee6875", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "finished"}]}}, "typeVersion": 2.2}, {"id": "8bb80330-daf1-4414-8c28-b33ad3a98f48", "name": "Wait5", "type": "n8n-nodes-base.wait", "position": [3040, 1380], "webhookId": "20e97cfa-ebde-4090-a739-a2a7b087bdf5", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "c5d33459-7bfb-48d7-b867-c51eecf083ad", "name": "Extracting Audio from Clips", "type": "n8n-nodes-base.httpRequest", "position": [3200, 1260], "parameters": {"url": "https://lemolex.app.n8n.cloud/webhook/8caa81c9-4322-4b79-8d69-045473776e51", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "podcast_url", "value": "={{ $('Send the YouTube Video Links').item.json['YouTube Video URL'] }}"}, {"name": "startSeconds", "value": "={{ $('Loop Over Clips').first().json.start_index }}"}, {"name": "endSeconds", "value": "={{ $('Loop Over Clips').first().json.end_index }}"}]}}, "typeVersion": 4.2}, {"id": "bcf6548b-6121-42a7-aac7-11108737ec21", "name": "Extracting Audio from Main Clip 2", "type": "n8n-nodes-base.httpRequest", "position": [3460, 1280], "parameters": {"url": "https://lemolex.app.n8n.cloud/webhook/40a697b1-58ba-4ce6-9e52-55cb0cfebafa", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "job-id", "value": "={{ $('Extracting Audio from Clips').item.json.id }}"}]}}, "typeVersion": 4.2}, {"id": "93c9a937-6cc2-4bbc-a615-6ebda8a9a129", "name": "If7", "type": "n8n-nodes-base.if", "position": [3660, 1280], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "17476aa1-49ae-4697-aa41-95f00dee6875", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "finished"}]}}, "typeVersion": 2.2}, {"id": "adc0561d-b16f-4318-836c-946e1ee6807c", "name": "Wait6", "type": "n8n-nodes-base.wait", "position": [3800, 1400], "webhookId": "8f686c54-b275-45be-9590-0f2bfed4c8c3", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "f9703d8d-585c-42a9-a9ec-8d2ee637452c", "name": "Download Clip Audio", "type": "n8n-nodes-base.httpRequest", "position": [4060, 1300], "parameters": {"url": "={{ $('If7').item.json['download-url'] }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "data-audio"}}}}, "typeVersion": 4.2}, {"id": "40a6e303-047d-404f-8d44-0da0dcb8981a", "name": "Clip Length Calculation", "type": "n8n-nodes-base.code", "position": [2220, 1180], "parameters": {"jsCode": "// Get the two numbers from the incoming JSON (from the previous node)\nconst a = $('Loop Over Clips').first().json.end_index;\nconst b = $('Loop Over Clips').first().json.start_index;\n\n// Calculate the interval; if you always want a positive result, use Math.abs:\nconst interval = Math.abs(a - b) + 3;\n\n// Return a new item with your result under whatever key you like\nreturn [\n  { json: { interval } }\n];\n"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8f0c8648-71a6-4a05-b09b-3745209ca946", "connections": {"If": {"main": [[{"node": "Extract Results", "type": "main", "index": 0}], [{"node": "Wait 1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Structure Links", "type": "main", "index": 0}], [{"node": "Wait1", "type": "main", "index": 0}]]}, "If4": {"main": [[{"node": "Download Audio Stage 3", "type": "main", "index": 0}], [{"node": "Wait3", "type": "main", "index": 0}]]}, "If5": {"main": [[{"node": "Clip Length Calculation", "type": "main", "index": 0}], [{"node": "Wait4", "type": "main", "index": 0}]]}, "If6": {"main": [[{"node": "Extracting Audio from Clips", "type": "main", "index": 0}], [{"node": "Wait5", "type": "main", "index": 0}]]}, "If7": {"main": [[{"node": "Download Clip Audio", "type": "main", "index": 0}], [{"node": "Wait6", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Clip Ready?", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "Download Audio Stage 2", "type": "main", "index": 0}]]}, "Wait4": {"main": [[{"node": "Extracting Main Clip", "type": "main", "index": 0}]]}, "Wait5": {"main": [[{"node": "Extracting Background Clip", "type": "main", "index": 0}]]}, "Wait6": {"main": [[{"node": "Extracting Audio from Main Clip 2", "type": "main", "index": 0}]]}, "Wait 1": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Loop Over Clips", "type": "main", "index": 0}]]}, "Submit Job": {"main": [[{"node": "Wait 1", "type": "main", "index": 0}]]}, "Clip Ready?": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Editing Clips": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Generate Title": {"main": [[{"node": "Download Final Clip", "type": "main", "index": 0}]]}, "Post To TikTok": {"main": [[{"node": "Posting Interval", "type": "main", "index": 0}]]}, "Extract Results": {"main": [[{"node": "Structure Transcription", "type": "main", "index": 0}]]}, "Loop Over Clips": {"main": [[], [{"node": "Generating Main Clip", "type": "main", "index": 0}]]}, "Structure Links": {"main": [[{"node": "Generate Title", "type": "main", "index": 0}]]}, "Posting Interval": {"main": [[{"node": "Loop Over Clips", "type": "main", "index": 0}]]}, "Audio Transcription": {"main": [[{"node": "Structure Transcription 2", "type": "main", "index": 0}]]}, "Download Clip Audio": {"main": [[{"node": "Audio Transcription", "type": "main", "index": 0}]]}, "Download Final Clip": {"main": [[{"node": "Post To TikTok", "type": "main", "index": 0}]]}, "Extracting Main Clip": {"main": [[{"node": "If5", "type": "main", "index": 0}]]}, "Generating Main Clip": {"main": [[{"node": "Extracting Main Clip", "type": "main", "index": 0}]]}, "Download Audio Stage 1": {"main": [[{"node": "Download Audio Stage 2", "type": "main", "index": 0}]]}, "Download Audio Stage 2": {"main": [[{"node": "If4", "type": "main", "index": 0}]]}, "Download Audio Stage 3": {"main": [[{"node": "Transcribe Podcast Audio", "type": "main", "index": 0}]]}, "Clip Length Calculation": {"main": [[{"node": "Generating Background Clip", "type": "main", "index": 0}]]}, "Structure Transcription": {"main": [[{"node": "Podcast Best Moments Extraction", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Podcast Best Moments Extraction", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Podcast Best Moments Extraction", "type": "ai_outputParser", "index": 0}]]}, "Transcribe Podcast Audio": {"main": [[{"node": "Submit Job", "type": "main", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Generate Title", "type": "ai_languageModel", "index": 0}]]}, "Structure Transcription 2": {"main": [[{"node": "Editing Clips", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Generate Title", "type": "ai_outputParser", "index": 0}]]}, "Extracting Background Clip": {"main": [[{"node": "If6", "type": "main", "index": 0}]]}, "Generating Background Clip": {"main": [[{"node": "Extracting Background Clip", "type": "main", "index": 0}]]}, "Extracting Audio from Clips": {"main": [[{"node": "Extracting Audio from Main Clip 2", "type": "main", "index": 0}]]}, "Send the YouTube Video Links": {"main": [[{"node": "Download Audio Stage 1", "type": "main", "index": 0}]]}, "Podcast Best Moments Extraction": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "Extracting Audio from Main Clip 2": {"main": [[{"node": "If7", "type": "main", "index": 0}]]}}}