{"id": "7ITgmmCKTMFGlTYD", "meta": {"instanceId": "84ad02d6104594179f43f1ce9cfe3a81637b2faedb57dafcb9e649b7542988db", "templateCredsSetupCompleted": true}, "name": "Competitor tracking on CrunchBase", "tags": [], "nodes": [{"id": "9bed760c-bd66-46b6-b949-08316d073237", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "14b8319e-bba0-4b59-9ecb-c3ddb212738d", "name": "Set Competitor Name", "type": "n8n-nodes-base.set", "position": [220, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0edc5749-a4da-4148-8ebd-9e21aa9b419e", "name": "Competitor", "type": "string", "value": "OpenAI"}]}}, "typeVersion": 3.4}, {"id": "9dfe1f50-14a8-40b9-a229-0afdf4f84a86", "name": "Generate Crunchbase Slug", "type": "n8n-nodes-base.code", "position": [440, 0], "parameters": {"jsCode": "// Convert \"Competitor\" field into Crunchbase slug format\nreturn items.map(item => {\n  const name = item.json.Competitor || \"\";\n\n  // Convert to lowercase, remove special chars, replace spaces with dashes\n  const slug = name\n    .toLowerCase()\n    .trim()\n    .replace(/[^a-z0-9\\s-]/g, \"\")   // Remove non-alphanumeric characters\n    .replace(/\\s+/g, \"-\");          // Replace spaces with hyphens\n\n  return {\n    json: {\n      ...item.json,\n      slug\n    }\n  };\n});\n"}, "typeVersion": 2}, {"id": "0c7107f4-1a7a-4a50-a259-b14db106ddd5", "name": "Fetch Crunchbase Data", "type": "n8n-nodes-base.httpRequest", "position": [740, 0], "parameters": {"url": "=https://api.crunchbase.com/api/v4/entities/organizations/{{ $json.slug }}", "options": {}, "sendBody": true, "sendQuery": true, "bodyParameters": {"parameters": [{}]}, "queryParameters": {"parameters": [{"name": "user_key", "value": "YOUR_API_KEY"}]}}, "typeVersion": 4.2}, {"id": "7f98afae-d216-4037-b475-2b37ce65f3c8", "name": "Create Review Task in ClickUp", "type": "n8n-nodes-base.clickUp", "position": [980, 0], "parameters": {"name": "Review Crunchbase Update and inform your manager", "additionalFields": {"status": "", "content": "=Company: {{ $json.data.properties.name }}\nLast Updated: {{ $json.data.properties.updated_at }}\nDescription: {{ $json.data.properties.short_description }}\nTotal Funding: {{ $json.data.properties.total_funding_usd }}\nLast Funding: {{ $json.data.properties.last_funding_type }}\nHomepage: {{ $json.data.properties.homepage_url }}", "dueDate": "", "priority": 3, "assignees": []}}, "typeVersion": 1}, {"id": "24fd0d4e-a329-4c65-9b7c-f6b30dd3b7a2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-40, -1900], "parameters": {"color": 3, "width": 640, "height": 2160, "content": "## 🔹 **SECTION 1: Input & Preparation 🔧✏️🧠**\n\n### 🧩 Nodes Included:\n\n1. 🔘 **Manual Trigger**\n2. 📝 **Set Competitor Name**\n3. 🧠 **Generate Crunchbase Slug**\n\n---\n\n### 1️⃣ 🔘 **Manual Trigger** — `Manual Trigger`\n\n🚀 **What it does:**\nThis node allows you to **manually run the workflow** during testing or setup. It doesn't trigger automatically on a schedule, but gives you control to click and run whenever you want.\n\n🧰 **Why it's useful:**\nPerfect for **debugging** and **testing**. Instead of waiting for a scheduled run, you can instantly see what the workflow does.\n\n---\n\n### 2️⃣ 📝 **Set Competitor Name** — `Set Competitor Name`\n\n🛠 **What it does:**\nThis node manually defines the company (competitor) you're tracking on Crunchbase.\n\n🔡 **Example input:**\n\n```json\n{\n  \"Competitor\": \"Stripe, Inc.\"\n}\n```\n\n💡 **Why it's useful:**\nAllows you to customize which competitor to check — one at a time — while testing. Later, you could replace this with a list from a database or Google Sheet for full automation.\n\n---\n\n### 3️⃣ 🧠 **Generate Crunchbase Slug** — `Generate Crunchbase Slug`\n\n🔄 **What it does:**\nConverts the raw company name into a **Crunchbase-friendly slug** (like a web-safe name) used in the Crunchbase API call.\n\n🔡 **Example:**\n\n* `\"Stripe, Inc.\"` → `\"stripe-inc\"`\n\n🧠 **How it works:**\n\n* Lowercases the text\n* Removes punctuation\n* Replaces spaces with hyphens (`-`)\n\n📌 **Why it's important:**\nCrunchbase’s API requires company slugs in the URL, not full names. This function ensures you get the correct format every time.\n\n---\n\n### ✅ **End of Section 1:**\n\nYou now have a properly formatted `slug` for a company like `openai` or `stripe-inc`, ready to query Crunchbase for real-time data.\n\n---\n\n"}, "typeVersion": 1}, {"id": "47a23c5d-e6ce-4ea2-b2e5-101fd9d02189", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [660, -1560], "parameters": {"color": 5, "width": 580, "height": 1820, "content": "## 🔹 **SECTION 2: Fetch + Create Task 📡🗂**\n\n### 🧩 Nodes Included:\n\n4. 🌐 **Fetch Crunchbase Data**\n5. 📋 **Create Review Task in ClickUp**\n\n---\n\n### 4️⃣ 🌐 **Fetch Crunchbase Data** — `Fetch Crunchbase Data`\n\n🔍 **What it does:**\nMakes a real-time API call to **Crunchbase** using the generated `slug`, and pulls in key information about the company — like funding, description, homepage, and last updated date.\n\n🔑 **Requires:** Crunchbase API Key\n\n🔡 **Example API Call:**\n\n```http\nGET https://api.crunchbase.com/api/v4/entities/organizations/stripe-inc?user_key=YOUR_API_KEY\n```\n\n🧾 **Sample Data Retrieved:**\n\n* `name`: Stripe\n* `updated_at`: 2025-06-05T12:34:56Z\n* `total_funding_usd`: 10B+\n* `homepage_url`: [https://stripe.com](https://stripe.com)\n* `short_description`: Online payment infrastructure\n\n📌 **Why it's useful:**\nThis gives you all the relevant data about the competitor's recent activities that might be worth your team's attention.\n\n---\n\n### 5️⃣ 📋 **Create Review Task in ClickUp** — `Create Review Task in ClickUp`\n\n🗂 **What it does:**\nAutomatically creates a new **task in ClickUp**, assigning the Crunchbase update to your team for review.\n\n📄 **Task Includes:**\n\n* 🏷 Title: `Review Crunchbase Update: Stripe`\n* 📝 Description:\n\n  ```text\n  Company: Stripe\n  Last Updated: 2025-06-05\n  Description: Online payment infrastructure\n  Total Funding: $10B\n  Website: https://stripe.com\n  ```\n\n💡 **Why it's powerful:**\nNo manual tracking or checking needed — your team is alerted **automatically** when there’s something new. It ensures nothing slips through the cracks.\n\n---\n\n"}, "typeVersion": 1}, {"id": "60181cc2-f735-4db3-87d0-db0fc89dc336", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-1880, -1900], "parameters": {"color": 4, "width": 1300, "height": 320, "content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\n"}, "typeVersion": 1}, {"id": "8cd558b6-8353-4d9d-af68-7ea02cef2b73", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1880, -1560], "parameters": {"color": 4, "width": 1289, "height": 3478, "content": " ✨ **Crunchbase → ClickUp Automation**\n\nTrack competitor updates from **Crunchbase** and automatically create **ClickUp tasks** for your team to review — all in just a few clicks!\n\n---\n\n## 🔹 **SECTION 1: Input & Preparation 🔧✏️🧠**\n\n### 🧩 Nodes Included:\n\n1. 🔘 **Manual Trigger**\n2. 📝 **Set Competitor Name**\n3. 🧠 **Generate Crunchbase Slug**\n\n---\n\n### 1️⃣ 🔘 **Manual Trigger** — `Manual Trigger`\n\n🚀 **What it does:**\nThis node allows you to **manually run the workflow** during testing or setup. It doesn't trigger automatically on a schedule, but gives you control to click and run whenever you want.\n\n🧰 **Why it's useful:**\nPerfect for **debugging** and **testing**. Instead of waiting for a scheduled run, you can instantly see what the workflow does.\n\n---\n\n### 2️⃣ 📝 **Set Competitor Name** — `Set Competitor Name`\n\n🛠 **What it does:**\nThis node manually defines the company (competitor) you're tracking on Crunchbase.\n\n🔡 **Example input:**\n\n```json\n{\n  \"Competitor\": \"Stripe, Inc.\"\n}\n```\n\n💡 **Why it's useful:**\nAllows you to customize which competitor to check — one at a time — while testing. Later, you could replace this with a list from a database or Google Sheet for full automation.\n\n---\n\n### 3️⃣ 🧠 **Generate Crunchbase Slug** — `Generate Crunchbase Slug`\n\n🔄 **What it does:**\nConverts the raw company name into a **Crunchbase-friendly slug** (like a web-safe name) used in the Crunchbase API call.\n\n🔡 **Example:**\n\n* `\"Stripe, Inc.\"` → `\"stripe-inc\"`\n\n🧠 **How it works:**\n\n* Lowercases the text\n* Removes punctuation\n* Replaces spaces with hyphens (`-`)\n\n📌 **Why it's important:**\nCrunchbase’s API requires company slugs in the URL, not full names. This function ensures you get the correct format every time.\n\n---\n\n### ✅ **End of Section 1:**\n\nYou now have a properly formatted `slug` for a company like `openai` or `stripe-inc`, ready to query Crunchbase for real-time data.\n\n---\n\n## 🔹 **SECTION 2: Fetch + Create Task 📡🗂**\n\n### 🧩 Nodes Included:\n\n4. 🌐 **Fetch Crunchbase Data**\n5. 📋 **Create Review Task in ClickUp**\n\n---\n\n### 4️⃣ 🌐 **Fetch Crunchbase Data** — `Fetch Crunchbase Data`\n\n🔍 **What it does:**\nMakes a real-time API call to **Crunchbase** using the generated `slug`, and pulls in key information about the company — like funding, description, homepage, and last updated date.\n\n🔑 **Requires:** Crunchbase API Key\n\n🔡 **Example API Call:**\n\n```http\nGET https://api.crunchbase.com/api/v4/entities/organizations/stripe-inc?user_key=YOUR_API_KEY\n```\n\n🧾 **Sample Data Retrieved:**\n\n* `name`: Stripe\n* `updated_at`: 2025-06-05T12:34:56Z\n* `total_funding_usd`: 10B+\n* `homepage_url`: [https://stripe.com](https://stripe.com)\n* `short_description`: Online payment infrastructure\n\n📌 **Why it's useful:**\nThis gives you all the relevant data about the competitor's recent activities that might be worth your team's attention.\n\n---\n\n### 5️⃣ 📋 **Create Review Task in ClickUp** — `Create Review Task in ClickUp`\n\n🗂 **What it does:**\nAutomatically creates a new **task in ClickUp**, assigning the Crunchbase update to your team for review.\n\n📄 **Task Includes:**\n\n* 🏷 Title: `Review Crunchbase Update: Stripe`\n* 📝 Description:\n\n  ```text\n  Company: Stripe\n  Last Updated: 2025-06-05\n  Description: Online payment infrastructure\n  Total Funding: $10B\n  Website: https://stripe.com\n  ```\n\n💡 **Why it's powerful:**\nNo manual tracking or checking needed — your team is alerted **automatically** when there’s something new. It ensures nothing slips through the cracks.\n\n---\n\n## 🎯 **Final Outcome**\n\nYou now have a streamlined mini-intelligence system:\n\n* ✅ Input a company\n* 🔁 Convert it into a slug\n* 📡 Fetch live data\n* 📝 Create an action task for your team\n\nAll **automated**, all **within n8n** — no coding required beyond smart node usage. ✨\n\n---\n\n"}, "typeVersion": 1}], "active": false, "pinData": {"Fetch Crunchbase Data": [{"json": {"data": {"uuid": "abc123", "properties": {"name": "OpenAI", "identifier": {"uuid": "abc123", "value": "OpenAI", "image_id": "v12345", "permalink": "organization/openai"}, "updated_at": "2025-06-05T17:32:12Z", "homepage_url": "https://openai.com", "last_funding_type": "Series F", "profile_image_url": "https://example.com/image.jpg", "short_description": "AI research and deployment company.", "total_funding_usd": 11300000000, "num_funding_rounds": 5}}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "162f7b36-530f-44a1-92a4-bcd5d20fe0df", "connections": {"Manual Trigger": {"main": [[{"node": "Set Competitor Name", "type": "main", "index": 0}]]}, "Set Competitor Name": {"main": [[{"node": "Generate Crunchbase Slug", "type": "main", "index": 0}]]}, "Fetch Crunchbase Data": {"main": [[{"node": "Create Review Task in ClickUp", "type": "main", "index": 0}]]}, "Generate Crunchbase Slug": {"main": [[{"node": "Fetch Crunchbase Data", "type": "main", "index": 0}]]}, "Create Review Task in ClickUp": {"main": [[]]}}}