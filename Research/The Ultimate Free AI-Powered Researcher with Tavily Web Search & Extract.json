{"id": "QqbYH25we4JDZrZD", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef"}, "name": "🔍🛠️ Tavily Search & Extract - Template", "tags": [], "nodes": [{"id": "e029204b-2e05-4262-b464-7c1b3a995f91", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-780, -940], "parameters": {"color": 4, "width": 520, "height": 940, "content": "## Tavily API Search Endpoint\n\n**Base URL**: `https://api.tavily.com/search`\n**Method**: POST\n\n### Required Parameters\n- `query`: The search query string\n- `api_key`: Your Tavily API key\n\n### Optional Parameters\n- `search_depth`: \"basic\" or \"advanced\" (default: \"basic\")\n- `topic`: \"general\" or \"news\" (default: \"general\") \n- `max_results`: Maximum number of results to return (default: 5)\n- `include_images`: Include query-related images (default: false)\n- `include_answer`: Include AI-generated answer (default: false)\n- `include_raw_content`: Include parsed HTML content (default: false)\n- `include_domains`: List of domains to include\n- `exclude_domains`: List of domains to exclude\n- `time_range`: Filter by time range (\"day\", \"week\", \"month\", \"year\")\n- `days`: Number of days back for news results (default: 3)\n\n### Example Request\n```json\n{\n    \"api_key\": \"tvly-YOUR_API_KEY\",\n    \"query\": \"Who is <PERSON>?\",\n    \"search_depth\": \"basic\",\n    \"include_answer\": false,\n    \"include_images\": true,\n    \"max_results\": 5\n}\n```\n"}, "typeVersion": 1}, {"id": "6c47edec-6c6e-460d-b098-f9a26caa5f8e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-220, -940], "parameters": {"color": 6, "width": 640, "height": 720, "content": "## Tavily API Extract Endpoint \n\n**Base URL**: `https://api.tavily.com/extract`\n**Method**: POST\n\n### Required Parameters\n- `urls`: Single URL string or array of URLs\n- `api_key`: Your Tavily API key\n\n### Optional Parameters\n- `include_images`: Include extracted images (default: false)\n\n### Example Request\n```json\n{\n    \"api_key\": \"tvly-YOUR_API_KEY\", \n    \"urls\": [\n        \"https://en.wikipedia.org/wiki/Artificial_intelligence\",\n        \"https://en.wikipedia.org/wiki/Machine_learning\"\n    ]\n}\n```"}, "typeVersion": 1}, {"id": "cacae1d1-c9ec-4c2f-ba5d-f782257697cc", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1240, -940], "parameters": {"color": 3, "width": 420, "height": 540, "content": "## Tavily API Documentation\n\nThe Tavily REST API provides seamless access to Tavily Search, a powerful search engine for LLM agents, and Tavily Extract, an advanced web scraping solution optimized for LLMs.\n\nhttps://docs.tavily.com/docs/rest-api/examples\n\nhttps://docs.tavily.com/docs/rest-api/api-reference#parameters\n\nThe Tavily API provides two main endpoints for search and data extraction.\n\nThe API returns JSON responses containing:\n\n- Search results with titles, URLs, and content\n- Extracted raw content from specified URLs\n- Response time metrics\n- Any error messages for failed requests\n\n\n**Note**: Error handling should check for failed results in the response before processing.\n"}, "typeVersion": 1}, {"id": "16e977f4-e72d-474c-a04b-3f3ad51cc322", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1240, -360], "parameters": {"width": 420, "height": 360, "content": "## Tavily Use Cases\n\n📜 Why Use Tavily API for Data Enrichment?\n\nhttps://docs.tavily.com/docs/use-cases/data-enrichment\n\n💡 Why Use Tavily API for Company Research?\n\nhttps://docs.tavily.com/docs/use-cases/company-research\n\n🔍 GPT Researcher\n\nhttps://docs.tavily.com/docs/gpt-researcher/introduction"}, "typeVersion": 1}, {"id": "7e4d0b3c-761d-42b9-bbbe-6ceb366fdc6f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [-580, -180], "parameters": {"url": "https://api.tavily.com/search", "body": "={\n    \"api_key\": \"tvly-YOUR_API_KEY\",\n    \"query\": \"What is n8n?\",\n    \"search_depth\": \"basic\",\n    \"include_answer\": false,\n    \"include_images\": true,\n    \"include_image_descriptions\": true,\n    \"include_raw_content\": false,\n    \"max_results\": 5,\n    \"include_domains\": [],\n    \"exclude_domains\": []\n}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "rawContentType": "application/json"}, "typeVersion": 4.2}, {"id": "47c0bfcf-a187-4b15-b208-2458c934d5f7", "name": "Tavily Extract", "type": "n8n-nodes-base.httpRequest", "position": [40, -400], "parameters": {"url": "https://api.tavily.com/extract", "body": "={\n    \"api_key\": \"tvly-YOUR_API_KEY\",\n    \"urls\": [\n        \"https://en.wikipedia.org/wiki/Artificial_intelligence\"\n    ]\n}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "rawContentType": "application/json"}, "typeVersion": 4.2}, {"id": "47791d39-087b-4104-aa0d-ef98deee945c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1940, -1020], "parameters": {"color": 7, "width": 660, "height": 1020, "content": "## Tavily API Overview\nhttps://docs.tavily.com/docs/welcome\n\nThe Tavily API provides a specialized search engine built specifically for AI agents and LLM applications, offering two main endpoints:\n\n## Search Endpoint\n\nThe search endpoint enables intelligent web searching with:\n\n**Key Features**\n- Query-based search with customizable depth (\"basic\" or \"advanced\")\n- Topic filtering for general or news content\n- Control over result quantity and content type\n- Domain inclusion/exclusion capabilities\n- Time range filtering and news date restrictions\n\n## Extract Endpoint\n\nThe extract endpoint focuses on content retrieval:\n\n**Key Features**\n- Single or batch URL processing\n- Raw content extraction\n- Optional image extraction\n- Structured response format\n\n## Implementation Benefits\n\n**For AI Integration**\n- Optimized for RAG (Retrieval Augmented Generation)\n- Single API call handles searching, scraping and filtering\n- Customizable response formats\n- Built-in content relevance scoring\n\n**Technical Advantages**\n- JSON response format\n- Error handling for failed requests\n- Response time metrics\n- Flexible content filtering options\n\n\nThis API is designed to simplify the integration of real-time web data into AI applications while ensuring high-quality, relevant results through intelligent processing and filtering."}, "typeVersion": 1}, {"id": "76b291bc-8c34-44f1-b366-09c9f51089e2", "name": "Get Top Result", "type": "n8n-nodes-base.set", "position": [-700, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a73e848c-f7e7-4b3a-ae99-930c577b47be", "name": "results", "type": "object", "value": "={{ $json.results.first() }}"}]}}, "typeVersion": 3.4}, {"id": "4b098e57-eff2-4e70-9429-23b5c3d936c2", "name": "Tavily Extract Top Search", "type": "n8n-nodes-base.httpRequest", "position": [-480, 140], "parameters": {"url": "https://api.tavily.com/extract", "body": "={\n    \"api_key\": \"{{ $('Tavily API Key').item.json.api_key }}\",\n    \"urls\": [\n        \"{{ $json.results.url }}\"\n    ]\n}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "rawContentType": "application/json"}, "typeVersion": 4.2}, {"id": "f593e164-1c9d-46e6-a619-39fe621c829f", "name": "Filter > 90%", "type": "n8n-nodes-base.set", "position": [-920, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8fd0cfc4-7adc-45f9-a278-d217e362ebfb", "name": "results", "type": "array", "value": "={{ $json.results.filter(item => item.score > 0.80) }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "fadd100c-0335-42c2-9c3d-48e6d17eb2f9", "name": "Tavily Search Topic", "type": "n8n-nodes-base.httpRequest", "position": [-1140, 140], "parameters": {"url": "https://api.tavily.com/search", "body": "={\n    \"api_key\": \"{{ $json.api_key }}\",\n    \"query\": \"{{ $('Provide search topic via Chat window').item.json.chatInput }}\",\n    \"search_depth\": \"basic\",\n    \"include_answer\": false,\n    \"include_images\": true,\n    \"include_image_descriptions\": true,\n    \"include_raw_content\": false,\n    \"max_results\": 5,\n    \"include_domains\": [],\n    \"exclude_domains\": []\n}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "rawContentType": "application/json"}, "typeVersion": 4.2}, {"id": "1bc5a21f-0f96-4951-9c88-0bec00b9c586", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-240, 300], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "994bb3ee-598b-4d3f-bcfc-16c9cca36657", "name": "Summarize Web Page Content", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-260, 140], "parameters": {"text": "=Summarize this web content and provide in Markdown format:  {{ $json.results[0].raw_content }}", "promptType": "define"}, "typeVersion": 1.5}, {"id": "d5520da7-f6bc-470e-ab96-e04097041f08", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1680, 40], "parameters": {"color": 5, "width": 1800, "height": 400, "content": "## Tavily Search and Extract with AI Summarization Example"}, "typeVersion": 1}, {"id": "9bd6c18e-aabf-4719-b9c4-ac91b36891a1", "name": "Tavily API Key", "type": "n8n-nodes-base.set", "position": [-1360, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "035660a9-bb58-4ecb-bad3-7f4d017fa69f", "name": "api_key", "type": "string", "value": "tvly-YOUR_API_KEY"}]}}, "typeVersion": 3.4}, {"id": "41f36ad7-7a2b-4732-89ec-fe6500768631", "name": "Provide search topic via Chat window", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-1580, 140], "webhookId": "6b8f316b-776e-429a-8699-55f230c3a168", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "0213756a-35c4-46a8-9b79-2e8a81852177", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1420, 320], "parameters": {"color": 7, "height": 80, "content": "### Tavily API Key\nhttps://app.tavily.com/home"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e1f22fbb-9663-405c-b7b1-7e8b2d54ad0f", "connections": {"Filter > 90%": {"main": [[{"node": "Get Top Result", "type": "main", "index": 0}]]}, "Get Top Result": {"main": [[{"node": "Tavily Extract Top Search", "type": "main", "index": 0}]]}, "Tavily API Key": {"main": [[{"node": "Tavily Search Topic", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Summarize Web Page Content", "type": "ai_languageModel", "index": 0}]]}, "Tavily Search Topic": {"main": [[{"node": "Filter > 90%", "type": "main", "index": 0}]]}, "Tavily Extract Top Search": {"main": [[{"node": "Summarize Web Page Content", "type": "main", "index": 0}]]}, "Provide search topic via Chat window": {"main": [[{"node": "Tavily API Key", "type": "main", "index": 0}]]}}}