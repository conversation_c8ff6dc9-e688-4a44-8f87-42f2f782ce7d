{"id": "EnfvHdczSXHN8vNv", "meta": {"instanceId": "dede14b31ec7e508c14f42cff0a64c12ba101f85945f0d41134b60824d8105f1", "templateId": "2860", "templateCredsSetupCompleted": true}, "name": "Resume Screening & Behavioral Interviews with Gemini, Elevenlabs, & Notion ATS copy", "tags": [], "nodes": [{"id": "eb481f48-a0bb-43b6-bb6f-bd6de416ed3c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1480, 700], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "3d98e145-b7c7-482a-8510-3ab6e442f65e", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [3180, 880], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"resume_score\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"resume_evaluation\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "ad85623d-7c18-4b80-b5a9-3515096e2917", "name": "HR Expert", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3000, 700], "parameters": {"text": "=Profile received:\n{{ $json.job_description }}\n\nCandidate:\n{{ $('Applicant Summary').item.json.response.text }}", "messages": {"messageValues": [{"message": "You are an HR expert and you need to understand if the candidate aligns with the profile sought by the company. You must give a score from 1 to 10, where 1 means the candidate does not align with what is required, while 10 means they are the ideal candidate because they fully reflect the desired profile. Furthermore, in the 'consideration' field, explain the reasoning behind your score."}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "23cb1def-11e8-4bcf-a667-154a9699c45d", "name": "Upload CV", "type": "n8n-nodes-base.googleDrive", "position": [660, 1120], "parameters": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "19gFV-OtPby1Q7OCFJYWFgf1HsMhmk7yJ", "cachedResultUrl": "https://drive.google.com/drive/folders/19gFV-OtPby1Q7OCFJYWFgf1HsMhmk7yJ", "cachedResultName": "[CV]"}, "inputDataFieldName": "Resume"}, "credentials": {"googleDriveOAuth2Api": {"id": "JjRf0Foc59YXzEmS", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "37ebc9bb-1d72-447c-8ea0-370b67a738e9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1060, 520], "parameters": {"width": 360, "height": 480, "content": "## Applicant Qualifications\n### Creates individual summary for Education, Job History, and Skills that is sent to LLM for processing; captures total years of experience"}, "typeVersion": 1}, {"id": "cfd19f7f-8790-4f2e-9ed2-a84b36b5613f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1620, 520], "parameters": {"width": 360, "height": 400, "content": "## Applicant Summary \n### Writes a concise summary of applicant’s Education, Job History, and Skills."}, "typeVersion": 1}, {"id": "41dc78d2-a937-49b2-8c3a-b73d841f053f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2940, 520], "parameters": {"width": 360, "height": 400, "content": "## HR Expert Evaluation\n### Compares resume to job description in Notion ATS and assesses candidate, outputting evaluation rationale and score of 1 to 10"}, "typeVersion": 1}, {"id": "bbf2ff07-8a53-4ca7-8e21-08f3bffc3ffa", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2040, 520], "parameters": {"width": 300, "height": 400, "content": "## Gets Job Description \n### Searches Notion ATS database and pulls description that matches Job Code in Applicant form trigger"}, "typeVersion": 1}, {"id": "fbba47e9-2e7c-42df-9da1-1893b238abc1", "name": "Extract Resume", "type": "n8n-nodes-base.extractFromFile", "position": [900, 700], "parameters": {"options": {}, "operation": "pdf", "binaryPropertyName": "=Resume"}, "typeVersion": 1}, {"id": "b1313d9a-0d52-4387-b6b3-e1f9ed24b7ce", "name": "Applicant Summary", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1680, 700], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "=", "combineMapPrompt": "=Write a concise summary of the following:\n\nEducational qualification: {{ $json.output[\"Educational qualification\"] }}\nJob History: {{ $json.output[\"Job History\"] }}\nSkills: {{ $json.output.Skills }}\n\nUse 300 words or less. Be concise and conversational."}}}}, "typeVersion": 2}, {"id": "a1d840d2-af90-4411-bfa7-d378ff6b4872", "name": "Job Description Summary", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [2420, 700], "parameters": {"text": "={{ $json.property_job_description }}", "messages": {"messageValues": [{"message": "summarize this in less than 250 words"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "627b3ddf-93eb-4206-84cb-6b0c78bb1e8f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [3720, 520], "parameters": {"width": 280, "height": 400, "content": "## Creates ATS Record\n### Updates Notion ATS database (free template) with applicant information, including AI assessment of qualifications vs job description."}, "typeVersion": 1}, {"id": "d646da88-23c2-4846-8494-9e84d019b13e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [3380, 520], "parameters": {"width": 280, "height": 400, "content": "## Creates G-Sheets Record\n### Updates Google Sheet with applicant data as source for compliance reporting"}, "typeVersion": 1}, {"id": "b3bac51c-b4db-4fe6-8bc2-bd5a3b798b3d", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2380, 520], "parameters": {"width": 360, "height": 400, "content": "## Job Description Summary\n### Summarizes the job description into a string, 250 words or less"}, "typeVersion": 1}, {"id": "4070358f-e169-40fb-8ba2-857149d8e37b", "name": "Applicant Qualifications", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1100, 840], "parameters": {"text": "={{ $json.text }}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}, "attributes": {"attributes": [{"name": "Educational qualification", "required": true, "description": "Summary of academic career, focusing on undergraduate and university studies.  Summarize in 100 words maximum."}, {"name": "Job History", "required": true, "description": "Work history summary, focusing on most recent work experiences. Summarize in 100 words maximum"}, {"name": "Skills", "required": true, "description": "Extract the candidate’s technical skills. What software, frameworks, functional skills they are proficient in. Make a bulleted list."}, {"name": "Experience", "required": true, "description": "Extract years of experience and group experience by job function or role type. Format Example: Total Years Exp: 7 - Account Executive: 2 years - Sales Development Representative: 2 years - Account Manager: 3 years"}, {"name": "Title & Employer", "description": "Extract most recent Job Title and Employer"}, {"name": "Total Years Experience", "description": "Extract total years of experience and format as Total Years Exp:  "}]}}, "typeVersion": 1}, {"id": "28996d39-49af-40fa-adf7-0e431d4d7ffe", "name": "Applicant Personal Data", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1100, 700], "parameters": {"text": "={{ $json.text }}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, include the  attribute's value as N/A."}, "schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"telephone\": {\n\t\t\t\"type\": \"string\",\n\t\t\t\"description\": \"Phone number of the contact (digits only)\"\n\t\t},\n\t\t\"city\": {\n\t\t\t\"type\": \"string\",\n\t\t\t\"description\": \"City of the contact\"\n\t\t},\n\t\t\"full_name\": {\n\t\t\t\"type\": \"string\",\n\t\t\t\"description\": \"Full name of the contact\"\n\t\t},\n\t\t\"email\": {\n\t\t\t\"type\": \"string\",\n\t\t\t\"format\": \"email\",\n\t\t\t\"description\": \"Email address of the contact\"\n\t\t}\n\t},\n\t\"required\": [\n\t\t\"full_name\",\n\t\t\"email\",\n\t\t\"telephone\",\n\t\t\"city\"\n\t]\n}"}, "typeVersion": 1}, {"id": "5c466cca-7be7-4cda-bcba-68acc6d80c15", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [560, 520], "parameters": {"width": 300, "height": 400, "content": "## Application Data\n### Captures data elements from Application Form and provides as input for workflow "}, "typeVersion": 1}, {"id": "1316d194-e7be-4b9a-9f96-2c953529a9d2", "name": "Application Data", "type": "n8n-nodes-base.set", "position": [680, 700], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "240c052e-7799-4cc2-8d6d-ae521a469b0d", "name": "job_code", "type": "string", "value": "={{ $json.undefined }}"}, {"id": "612cdd0d-456d-4ebf-b8d8-8638b2b59390", "name": "date_time", "type": "string", "value": "={{ $json.submittedAt }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "3f8c6944-f23c-44c2-afe1-1479fe9f97cc", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [60, 480], "parameters": {"width": 360, "height": 660, "content": "  "}, "typeVersion": 1}, {"id": "9de7840a-a713-4f31-b7a2-a0dc80654281", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [4060, 640], "parameters": {"color": 4, "width": 500, "height": 180, "content": "## Human in the Loop 1 (Notion)\n### Hiring manager reviews qualified applicants in Notion ATS dashboard (free template) and triggers next steps with drag and drop that invites applicants to AI behavioral-based interview.  "}, "typeVersion": 1}, {"id": "ae5f6a5f-40c2-4f25-9df2-fd4565f61e2d", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [40, 120], "parameters": {"color": 3, "width": 4500, "content": "# Candidate Application > Resume Screen > ATS Record Creation > Invite to Interview\n## Automating the process from application to first round interview invitation."}, "typeVersion": 1}, {"id": "46211734-a52a-415e-8986-0da2af2c3a22", "name": "ElevenLabs Web Hook", "type": "n8n-nodes-base.webhook", "position": [140, 1880], "webhookId": "a3c17b54-7cd0-496a-af8a-74a6298dcfb4", "parameters": {"options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "1eecec11-38b1-493d-9198-e22ae2836034", "name": "ai_convo_items", "type": "n8n-nodes-base.set", "position": [440, 1880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4d283fef-ea58-4479-9ddb-f4cd6f89020d", "name": "criteria_1_result", "type": "string", "value": "={{ $json.body.data.analysis.evaluation_criteria_results.problem_solving.result }}"}, {"id": "0584a114-baa3-4744-bcb0-4c52ba2760c1", "name": "criteria_1_rationale", "type": "string", "value": "={{ $json.body.data.analysis.evaluation_criteria_results.problem_solving.rationale }}"}, {"id": "b0aee518-3da0-4d82-b31b-8d5db29fa697", "name": "criteria_2_result", "type": "string", "value": "={{ $json.body.data.analysis.evaluation_criteria_results.handling_escalated_issues.result }}"}, {"id": "d9b9a697-e89a-41a4-abf0-bef5e5d5379a", "name": "criteria_2_rationale", "type": "string", "value": "={{ $json.body.data.analysis.evaluation_criteria_results.handling_escalated_issues.rationale }}"}, {"id": "cc3576db-804e-4aae-acf4-9ea9f6ef5223", "name": "ai_screen_phone_number_value", "type": "string", "value": "={{ $json.body.data.analysis.data_collection_results.phone_number_AI_screen.value }}"}, {"id": "a6a65cb1-f5f2-4d05-900a-9b4d6242a993", "name": "ai_screen_full_name_value", "type": "string", "value": "={{ $json.body.data.analysis.data_collection_results.full_name.value }}"}, {"id": "665854b1-d0af-42c3-9e96-465872fd367c", "name": "ai_screen_call_time", "type": "string", "value": "={{ $json.body.data.conversation_initiation_client_data.dynamic_variables.system__time_utc }}"}, {"id": "f295a22c-2266-4906-acf1-de60697c7611", "name": "ai_screen_conversation_id", "type": "string", "value": "={{ $json.body.data.conversation_initiation_client_data.dynamic_variables.system__conversation_id }}"}, {"id": "39309fb6-223e-487e-ac7e-6b5ce6e9e243", "name": "full_transcript", "type": "string", "value": "={{ $json.body.data }}"}]}}, "typeVersion": 3.4}, {"id": "e8a451d9-83fa-46f8-9869-8d00a1122656", "name": "Extract_Audio", "type": "n8n-nodes-base.httpRequest", "position": [2120, 1880], "parameters": {"url": "=https://api.elevenlabs.io/v1/convai/conversations/{{ $node[\"ai_convo_items\"].json.ai_screen_conversation_id }}/audio", "options": {}, "jsonHeaders": "{\n  \"xi-api-key\":\"insert elevenlabs api key\"\n}\n", "sendHeaders": true, "specifyHeaders": "json"}, "typeVersion": 4.2}, {"id": "ed52a6c5-ac4e-4d70-9d15-8ca586c536c6", "name": "Filter_Notion_db", "type": "n8n-nodes-base.filter", "position": [1520, 1880], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a4107790-d8d6-4f65-bc0b-f7c33a573769", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.property_phone }}", "rightValue": "={{ $('ai_convo_items').item.json.ai_screen_phone_number_value }}"}]}}, "typeVersion": 2.2}, {"id": "95128237-3759-469c-94cb-87aefd02bd98", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [740, 1880], "parameters": {"text": "={{ $json.full_transcript }}", "options": {"systemMessage": "You are an AI agent embodying the role of a skilled and objective Talent Acquisition Specialist. Your primary purpose is to review and critically evaluate answers to behavior-based interview questions. Review the full transcript and provide an expert evaluation of the candidate, based on their answers, using the evaluation criteria in the attached Notion tool to form the basis of your assessment. \n\nFilter Criteria Instructions:\nSearch the Notion database (attached tool) for the evaluation_criteria that matches the Job or Position title in the transcript First Message.  \n\n\nProvide a written assessment of the overall interview in a concise summary less than 300 words. Also provide a score 1 low to 5 high for the overall interview and place this at the start of the assessment. Confirm if you were able to use the evaluation criteria to make your assessment. Format the output as a text string.\n\nExample Output:\n  \"Score: 2 | The candidate's responses...\""}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.8}, {"id": "1b38915c-53b2-4526-a8e0-4ad55d43dcc1", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [60, 1700], "parameters": {"width": 280, "height": 400, "content": "## Elevenlabs Trigger\n### AI Conversation agent behavior-based interview data/audio sent at end of conversation.  Includes an AI evaluation of interview questions.   \n"}, "typeVersion": 1}, {"id": "e198e96c-f46c-4043-abc2-be47ed9c799a", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [360, 1700], "parameters": {"width": 260, "height": 400, "content": "## Data Mapping\n### Conversation data elements, including evaluation criteria and transcript summary mapped as output fields\n"}, "typeVersion": 1}, {"id": "1505c84b-c06d-4397-adb0-5e0309a01271", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [660, 1700], "parameters": {"width": 440, "height": 400, "content": "## AI Agent Interview Assessment\n### AI agent reviews full conversation transcript and provides overall assessment of behavior based interview, scoring applicants from 1 low to 5 high.\n"}, "typeVersion": 1}, {"id": "3f8e282e-e2c3-44a3-bd88-fb5f1eef54ed", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [1140, 1700], "parameters": {"width": 260, "height": 400, "content": "## Applicant Tracker\n### Pulls applicant record from Notion db"}, "typeVersion": 1}, {"id": "0d33e767-e65a-47e7-b33c-3806b00f1c6a", "name": "Sticky Note18", "type": "n8n-nodes-base.stickyNote", "position": [1440, 1700], "parameters": {"width": 260, "height": 400, "content": "## Applicant ID\n### Using phone number captured during interview, matches interview with candidate record in db"}, "typeVersion": 1}, {"id": "1ce8d5e4-a576-45d5-9285-58f3cf00796d", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [1740, 1700], "parameters": {"width": 260, "height": 400, "content": "## Update Notion DB\n### Matches record and updates applicant record with AI conversation agent criteria evaluation and N8N AI agent overall interview score.\n"}, "typeVersion": 1}, {"id": "6d0fd6ad-0ead-4734-8ef4-f0e6effcd67c", "name": "Sticky Note20", "type": "n8n-nodes-base.stickyNote", "position": [2040, 1700], "parameters": {"width": 260, "height": 400, "content": "## Conversation Audio\n### Downloads conversation audio and saves to Google Drive.  Option to delete audio from Elevenlabs server.\n"}, "typeVersion": 1}, {"id": "6fd97a41-5ac4-4f93-85fb-296c450f1312", "name": "Sticky Note21", "type": "n8n-nodes-base.stickyNote", "position": [2900, 1820], "parameters": {"color": 4, "width": 720, "height": 180, "content": "## Human in the Loop 2 - (Notion)\n### Hiring manager reviews Notion ATS dashboard (free template) and views AI Agent’s, overall assessment of conversation, including score, and individual assessment of each question response.  Manager can then automatically schedule the next interview  by dragging the applicant profile to the next process stage in Notion dashboard.\n"}, "typeVersion": 1}, {"id": "38e36f98-0387-4267-b573-ad957338565d", "name": "Sticky Note22", "type": "n8n-nodes-base.stickyNote", "position": [60, 1340], "parameters": {"color": 3, "width": 4480, "content": "# Conversation AI Agent Interview > AI Assessment - Evaluation > Notion ATS Update with Audio transcript\n## Automating behavioral based interview evaluation and scoring; updating manager dashboard in Notion. "}, "typeVersion": 1}, {"id": "88a9d40c-9839-4fbb-9f78-81025cde86e7", "name": "Sticky Note23", "type": "n8n-nodes-base.stickyNote", "position": [-500, 320], "parameters": {"color": 5, "width": 520, "height": 980, "content": "# Application \n## Applicant initiates process from Notion hosted Career Page (free template), submitting application using N8N form embedded into job postings.  Hidden field in form, Job Code, matches applicant to position.  The current configuration enables the employer to run AI recruiting for 3 roles at the same time.  Templates can be expanded to accommodate more than 3 jobs."}, "typeVersion": 1}, {"id": "40c48633-6021-4e4d-a3bd-1b81433f9b64", "name": "Sticky Note24", "type": "n8n-nodes-base.stickyNote", "position": [-520, 1700], "parameters": {"color": 5, "width": 520, "height": 400, "content": " # AI Agent Interview\n## Successful candidates are invited to an instant interview with the AI agent"}, "typeVersion": 1}, {"id": "0447d46c-3863-46a6-ae44-36e812b8f5e5", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [60, 1160], "parameters": {"width": 360, "height": 140, "content": "### Configuration Note: \nUpdate Title and Job code in form to match your job posting hosted in Notion (free template) \n"}, "typeVersion": 1}, {"id": "ee57ca8f-7faf-4eae-a52f-bf25eb5462ca", "name": "Link Audio in Notion", "type": "n8n-nodes-base.notion", "position": [2640, 1880], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $('Applicant Record').item.json.id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Interview Audio|files", "fileUrls": {"fileUrl": [{"url": "=https://drive.google.com/file/d/{{ $json.id }}/preview", "name": "Interview Audio"}]}}]}}, "credentials": {"notionApi": {"id": "noqe7mtKHNObSPoE", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "2d081496-a85b-4d2f-b5a8-acabd18028d5", "name": "Sticky Note25", "type": "n8n-nodes-base.stickyNote", "position": [2520, 1700], "parameters": {"width": 340, "height": 400, "content": "## Embed audio transcript in Notion\n### Embeds audio transcript in applicant profile hosted in Notion ATS database, providing hiring manager easy access to validate AI assessment. \n"}, "typeVersion": 1}, {"id": "a319edea-a12d-4d70-85d6-9a03e8fafdaf", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-500, 120], "parameters": {"color": 5, "width": 520, "content": "# Workflow 1"}, "typeVersion": 1}, {"id": "bb415161-dd68-4c7b-baf6-a5190300d609", "name": "<PERSON><PERSON> Note26", "type": "n8n-nodes-base.stickyNote", "position": [-520, 1360], "parameters": {"color": 5, "width": 520, "height": 140, "content": "# Workflow 2"}, "typeVersion": 1}, {"id": "004dc3fe-2c4c-4a7c-82d7-f6737728a96f", "name": "Resume URL", "type": "n8n-nodes-base.set", "position": [880, 1120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c0230d7a-037b-4d69-a133-28a611fba010", "name": "resume_url", "type": "string", "value": "=https://drive.google.com/file/d/{{ $json.id }}/preview "}]}}, "typeVersion": 3.4}, {"id": "15aae5ce-7247-44cd-8235-804d992d9a14", "name": "Get Applicant Record", "type": "n8n-nodes-base.notion", "position": [1280, 1120], "parameters": {"filters": {"conditions": [{"key": "Resume |files", "condition": "is_empty"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "1cc7f9c9-6878-80c9-a271-ca0521f11b30", "_comment": "removed notion database above", "cachedResultUrl": "  ", "cachedResultName": "Applicant Tracker"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "noqe7mtKHNObSPoE", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "397c83d8-f3cd-4c06-9ab5-b8ddb55cf7b9", "name": "Embed Resume in Notion", "type": "n8n-nodes-base.notion", "position": [1500, 1120], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Resume |files", "fileUrls": {"fileUrl": [{"url": "={{ $('Resume URL').item.json.resume_url }}", "name": "Resume"}]}}]}}, "credentials": {"notionApi": {"id": "noqe7mtKHNObSPoE", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "998b0d29-22c9-448a-af1a-6bfe5a282cd6", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [1080, 1120], "webhookId": "71a63e24-b5a7-475f-b3d4-4f062d1caf41", "parameters": {}, "typeVersion": 1.1}, {"id": "76b38736-b3ec-4b52-85cf-0d9556c61541", "name": "Application Form 1 of 3", "type": "n8n-nodes-base.formTrigger", "position": [180, 520], "webhookId": "fbcca45f-efd3-4f31-9b0e-1ddb19705200", "parameters": {"options": {"buttonLabel": "Submit", "respondWithOptions": {"values": {"formSubmittedText": "Thank you for your interest in joining the [Company Name] team! We’ll review your information, and if your background looks like a match, we’ll reach out to schedule the next steps.  While we review your application, please know that we will not share your application information with anyone outside the [Company Name] team except where necessary to assist us in assessing your candidacy throughout the recruitment process. That means your data may be assessed in the United States by our team in [City/Location]. We may keep the information you submitted for up to four years and use it to keep you informed of other opportunities that might be a good fit for you.  If you would like to know more about how we use your personal data, please review our Privacy Notice, where you can also find information on how to update your contact preferences.  In the meantime, check out our company culture!  Thanks again,  The [Company Name] Talent Acquisition Team"}}}, "formTitle": "Sr Account Executive", "formFields": {"values": [{"fieldName": "job_code", "fieldType": "hiddenField", "fieldValue": "300"}, {"fieldLabel": "Name", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "requiredField": true}, {"fieldType": "file", "fieldLabel": "Resume", "requiredField": true, "acceptFileTypes": ".pdf"}]}, "formDescription": "[Company Name]"}, "typeVersion": 2.2}, {"id": "6ae41a72-fc0e-4e6f-a3ee-************", "name": "Application Form 2 of 3", "type": "n8n-nodes-base.formTrigger", "position": [180, 740], "webhookId": "0b2ad1e6-4867-4f4e-9eae-141b16266a2a", "parameters": {"options": {"buttonLabel": "Submit", "respondWithOptions": {"values": {"formSubmittedText": "Thank you for your interest in joining the [Company Name] team! We’ll review your information, and if your background looks like a match, we’ll reach out to schedule the next steps.  While we review your application, please know that we will not share your application information with anyone outside the [Company Name] team except where necessary to assist us in assessing your candidacy throughout the recruitment process. That means your data may be assessed in the United States by our team in [City/Location]. We may keep the information you submitted for up to four years and use it to keep you informed of other opportunities that might be a good fit for you.  If you would like to know more about how we use your personal data, please review our Privacy Notice, where you can also find information on how to update your contact preferences.  In the meantime, check out our company culture!  Thanks again,  The [Company Name] Talent Acquisition Team"}}}, "formTitle": "Full Stack Developer", "formFields": {"values": [{"fieldName": "Job Code", "fieldType": "hiddenField", "fieldValue": "200"}, {"fieldLabel": "Name", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "requiredField": true}, {"fieldType": "file", "fieldLabel": "Resume", "acceptFileTypes": ".pdf"}]}, "formDescription": "[Company Name]"}, "typeVersion": 2.2}, {"id": "daa5b254-2b07-4367-b330-c333dfb602d2", "name": "Application form 3 of 3", "type": "n8n-nodes-base.formTrigger", "position": [180, 960], "webhookId": "1224b9b0-05b1-4ed5-af9a-ccd7ff01eb6c", "parameters": {"options": {"buttonLabel": "Submit", "respondWithOptions": {"values": {"formSubmittedText": "Thank you for your interest in joining the [Company Name] team! We’ll review your information, and if your background looks like a match, we’ll reach out to schedule the next steps.  While we review your application, please know that we will not share your application information with anyone outside the [Company Name] team except where necessary to assist us in assessing your candidacy throughout the recruitment process. That means your data may be assessed in the United States by our team in [City/Location]. We may keep the information you submitted for up to four years and use it to keep you informed of other opportunities that might be a good fit for you.  If you would like to know more about how we use your personal data, please review our Privacy Notice, where you can also find information on how to update your contact preferences.  In the meantime, check out our company culture!  Thanks again,  The [Company Name] Talent Acquisition Team"}}}, "formTitle": "IT Support Analyst", "formFields": {"values": [{"fieldName": "Job Code", "fieldType": "hiddenField", "fieldValue": "100"}, {"fieldLabel": "Name"}, {"fieldType": "email", "fieldLabel": "Email"}, {"fieldType": "file", "fieldLabel": "Resume", "acceptFileTypes": ".pdf"}]}, "formDescription": "[Company Name]"}, "typeVersion": 2.2}, {"id": "368952ec-7b0c-4404-bbc8-1fa34e1c7ab1", "name": "Get Job Description", "type": "n8n-nodes-base.notion", "position": [2140, 700], "parameters": {"filters": {"conditions": [{"key": "Job Code|select", "condition": "equals", "selectValue": "={{ $('Application Data').item.json.job_code }}"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": {"__rl": true, "mode": "list", "value": "1d97f9c9-6878-80c1-bdca-e0e803367326", "_comment": "removed notion database above", "cachedResultUrl": "  ", "cachedResultName": "                             Work at [Company Name]"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "noqe7mtKHNObSPoE", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "8b4f2624-5b13-481e-8297-bbd9045dbd08", "name": "Job Description Mapping", "type": "n8n-nodes-base.set", "position": [2800, 700], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a3d049b0-5a70-4e7b-a6f2-81447da5282a", "name": "job_description", "type": "string", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 3.4}, {"id": "********-56b0-4001-91f1-64dcc5443d7e", "name": "Applicant Data Backup", "type": "n8n-nodes-base.googleSheets", "position": [3460, 700], "parameters": {"columns": {"value": {"YOE": "={{ $('Merge').item.json.output['Total Years Experience'] }}", "Name": "={{ $('Application Data').item.json.Name }}", "Phone": "={{ $('Merge').item.json.output.telephone }}", "Skills": "={{ $('Merge').item.json.output['Job History'] }}", "Job Code": "={{ $('Application Data').item.json.job_code }}", "Education": "={{ $('Merge').item.json.output['Educational qualification'] }}", "Job History": "={{ $('Merge').item.json.output['Job History'] }}", "Resume Score": "={{ $json.output.resume_score }}", "Hiring Manager": "={{ $('Get Job Description').item.json.property_hiring_manager }}", "Candidate Email": "={{ $('Merge').item.json.output.email }}", "Application Date": "={{ $('Application Data').item.json.date_time }}", "Applicant Location": "={{ $('Merge').item.json.output.city }}", "Experience Summary": "={{ $('Merge').item.json.output.Experience }}", "AI Resume Assessment": "={{ $json.output.resume_evaluation }}", "Hiring Manager Email": "={{ $('Get Job Description').item.json.property_hiring_manager_email }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Application Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Application Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Requisition Title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Requisition Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Resume Score", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Resume Score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "YOE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "YOE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Experience Summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Experience Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AI Resume Assessment", "type": "string", "display": true, "removed": false, "required": false, "displayName": "AI Resume Assessment", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Education", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Education", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job History", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Job History", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Skills", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Skills", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applicant Location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Applicant Location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Hiring Manager", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Hiring Manager", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Hiring Manager <PERSON><PERSON>", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Hiring Manager <PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Candidate <PERSON><PERSON>", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Candidate <PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job Code", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Job Code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "conversation_id", "type": "string", "display": true, "removed": true, "required": false, "displayName": "conversation_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Evaluation Criteria 1", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Evaluation Criteria 1", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Evaluation Criteria 2", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Evaluation Criteria 2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Evaluation Criteria 3", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Evaluation Criteria 3", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ai_interview_evaluation", "type": "string", "display": true, "removed": true, "required": false, "displayName": "ai_interview_evaluation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Interview Audio", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Interview Audio", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applicant Snapshot", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Applicant Snapshot", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1l4JFmEqY6CJdAICdr6fpetCMJSpU57wk2kduFwNx6fo/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1l4JFmEqY6CJdAICdr6fpetCMJSpU57wk2kduFwNx6fo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1l4JFmEqY6CJdAICdr6fpetCMJSpU57wk2kduFwNx6fo/edit?usp=drivesdk", "cachedResultName": "CV Agent Score Tracker (Simple)"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "j66JAhmq7Kt3rrJp", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "4980993a-8e70-4b4a-a37c-39732b5d5dd0", "name": "Create Applicant Record", "type": "n8n-nodes-base.notion", "position": [3820, 700], "parameters": {"title": "={{ $('Application Data').item.json.Name }} | {{ $('Merge').item.json.output['Title & Employer'] }}", "simple": false, "options": {}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1cc7f9c9-6878-80c9-a271-ca0521f11b30", "_comment": "removed notion database above", "cachedResultUrl": "  ", "cachedResultName": "Applicant Tracker"}, "propertiesUi": {"propertyValues": [{"key": "Resume Score|select", "selectValue": "={{ $('HR Expert').item.json.output.resume_score }}"}, {"key": "Candidate Email|email", "emailValue": "={{ $('Application Data').item.json.Email }}"}, {"key": "Applicant Location|rich_text", "textContent": "={{ $('Applicant Personal Data').item.json.output.city }}"}, {"key": "Phone|rich_text", "textContent": "={{ $('Merge').item.json.output.telephone }}"}, {"key": "Name|rich_text", "textContent": "={{ $('Application Data').item.json.Name }}"}, {"key": "Application Date|date", "date": "={{ $('Application Data').item.json.submittedAt }}", "includeTime": false}, {"key": "AI Resume Assessment |rich_text", "textContent": "={{ $('HR Expert').item.json.output.resume_evaluation }}"}, {"key": "Education|rich_text", "textContent": "={{ $('Applicant Qualifications').item.json.output['Educational qualification'] }}"}, {"key": "Job History|rich_text", "textContent": "={{ $('Applicant Qualifications').item.json.output['Job History'] }}"}, {"key": "Skills|rich_text", "textContent": "={{ $('Applicant Qualifications').item.json.output.Skills }}"}, {"key": "Job Code|rich_text", "textContent": "={{ $('Application Data').item.json.undefined }}"}, {"key": "Experience Summary|rich_text", "textContent": "={{ $('Merge').item.json.output.Experience }}"}, {"key": "YOE|rich_text", "textContent": "={{ $('Merge').item.json.output['Total Years Experience'] }}"}, {"key": "Applicant Snapshot|rich_text", "textContent": "={{ $('Applicant Summary').item.json.response.text }}"}, {"key": "Requisition Title|rich_text", "textContent": "={{ $('Get Job Description').item.json.name }}"}]}}, "credentials": {"notionApi": {"id": "noqe7mtKHNObSPoE", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "3d356f2f-ab7b-4c93-ad89-efd0c98c771b", "name": "Upload Audio to Drive", "type": "n8n-nodes-base.googleDrive", "position": [2360, 1880], "parameters": {"name": "={{ $json.name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "19gFV-OtPby1Q7OCFJYWFgf1HsMhmk7yJ", "cachedResultUrl": "https://drive.google.com/drive/folders/19gFV-OtPby1Q7OCFJYWFgf1HsMhmk7yJ", "cachedResultName": "[CV]"}}, "credentials": {"googleDriveOAuth2Api": {"id": "JjRf0Foc59YXzEmS", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "7dc66aee-2e13-4b73-952f-5c705d73f5e8", "name": "Google Gemini", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [2340, 1000], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-001"}, "credentials": {"googlePalmApi": {"id": "xi4CKZqHcbItLwLd", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "a1995f8a-4a77-401d-a7ef-0a6c08d9f4e3", "name": "Evaluation Criteria", "type": "n8n-nodes-base.notionTool", "position": [900, 2160], "parameters": {"filters": {"conditions": [{"key": "evaluation_criteria|rich_text", "condition": "is_not_empty"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "1d97f9c9-6878-80c1-bdca-e0e803367326", "_comment": "removed notion database above", "cachedResultUrl": "  ", "cachedResultName": "                             Work at [Company Name]"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "noqe7mtKHNObSPoE", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "604b0ad0-eb95-43e5-b0d7-045204be3158", "name": "Applicant Record", "type": "n8n-nodes-base.notion", "position": [1220, 1880], "parameters": {"simple": false, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "1cc7f9c9-6878-80c9-a271-ca0521f11b30", "_comment": "removed notion database above", "cachedResultUrl": "  ", "cachedResultName": "Applicant Tracker"}}, "credentials": {"notionApi": {"id": "noqe7mtKHNObSPoE", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "15dec2a1-2f1c-4975-aaaa-8057e79e1b23", "name": "Update_Applicant_Record", "type": "n8n-nodes-base.notion", "position": [1820, 1880], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Evaluation Criteria 1|rich_text", "textContent": "=Interview Criteria 1 | {{ $('ai_convo_items').item.json.criteria_1_result }} | {{ $('ai_convo_items').item.json.criteria_1_rationale }}"}, {"key": "Evaluation Criteria 2|rich_text", "textContent": "=Interview Criteria 2 | {{ $('ai_convo_items').item.json.criteria_2_result }} | {{ $('ai_convo_items').item.json.criteria_2_rationale }}"}, {"key": "ai_interview_evaluation|rich_text", "textContent": "={{ $('AI Agent').item.json.output }}"}]}}, "credentials": {"notionApi": {"id": "noqe7mtKHNObSPoE", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "b1ca7def-3f9e-42db-8bea-710f728d0867", "name": "Google Gemini Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [720, 2160], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "credentials": {"googlePalmApi": {"id": "xi4CKZqHcbItLwLd", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "6112c653-3dbb-44d9-90c4-e53cdc51e15f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1660, 760], "parameters": {}, "typeVersion": 1}, {"id": "9fadcb64-5ca9-4c6f-85fa-70a5af7a647c", "name": "Sticky Note27", "type": "n8n-nodes-base.stickyNote", "position": [60, 300], "parameters": {"width": 960, "content": "# Processes Application and extracts resume"}, "typeVersion": 1}, {"id": "1379a35f-3deb-41c8-bbca-ab4408398724", "name": "Sticky Note28", "type": "n8n-nodes-base.stickyNote", "position": [1060, 300], "parameters": {"width": 920, "content": "# Summarizes Applicant’s Education, Job History, and Skills"}, "typeVersion": 1}, {"id": "ad1383f9-a632-4f8d-805e-f360669efa66", "name": "Sticky Note29", "type": "n8n-nodes-base.stickyNote", "position": [2020, 300], "parameters": {"width": 860, "content": "# Gets role specific Job Description from Notion"}, "typeVersion": 1}, {"id": "ba4c201e-c346-43dd-bf3d-2cf7f5f26903", "name": "Sticky Note30", "type": "n8n-nodes-base.stickyNote", "position": [2940, 300], "parameters": {"width": 1040, "content": "# Scores Applicant’s resume, adding applicant record to Notion"}, "typeVersion": 1}, {"id": "b6df01a6-36c0-4173-bc2b-27bdb94296aa", "name": "Sticky Note31", "type": "n8n-nodes-base.stickyNote", "position": [60, 1520], "parameters": {"width": 560, "content": "# Receives AI Voice agent interview from Elevenlabs  "}, "typeVersion": 1}, {"id": "994a940e-27c4-4dd3-864b-40ca3b58c553", "name": "Sticky Note32", "type": "n8n-nodes-base.stickyNote", "position": [660, 1520], "parameters": {"width": 460, "content": "# Scores interview and provides assessment"}, "typeVersion": 1}, {"id": "b9487416-79bb-4137-ad88-d42a10543879", "name": "Sticky Note33", "type": "n8n-nodes-base.stickyNote", "position": [1140, 1520], "parameters": {"width": 1700, "content": "# Updates applicant record with AI Interview Assessment and Interview Audio file"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c8387ede-95e6-4e10-a830-38406ae49064", "connections": {"Wait": {"main": [[{"node": "Get Applicant Record", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Applicant Summary", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Applicant Record", "type": "main", "index": 0}]]}, "HR Expert": {"main": [[{"node": "Applicant Data Backup", "type": "main", "index": 0}]]}, "Upload CV": {"main": [[{"node": "Resume URL", "type": "main", "index": 0}]]}, "Resume URL": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Extract_Audio": {"main": [[{"node": "Upload Audio to Drive", "type": "main", "index": 0}]]}, "Google Gemini": {"ai_languageModel": [[{"node": "Applicant Summary", "type": "ai_languageModel", "index": 0}, {"node": "Applicant Qualifications", "type": "ai_languageModel", "index": 0}, {"node": "HR Expert", "type": "ai_languageModel", "index": 0}, {"node": "Applicant Personal Data", "type": "ai_languageModel", "index": 0}, {"node": "Job Description Summary", "type": "ai_languageModel", "index": 0}]]}, "Extract Resume": {"main": [[{"node": "Applicant Qualifications", "type": "main", "index": 0}, {"node": "Applicant Personal Data", "type": "main", "index": 0}]]}, "ai_convo_items": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Applicant Record": {"main": [[{"node": "Filter_Notion_db", "type": "main", "index": 0}]]}, "Application Data": {"main": [[{"node": "Extract Resume", "type": "main", "index": 0}]]}, "Filter_Notion_db": {"main": [[{"node": "Update_Applicant_Record", "type": "main", "index": 0}]]}, "Applicant Summary": {"main": [[{"node": "Get Job Description", "type": "main", "index": 0}]]}, "ElevenLabs Web Hook": {"main": [[{"node": "ai_convo_items", "type": "main", "index": 0}]]}, "Evaluation Criteria": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Get Job Description": {"main": [[{"node": "Job Description Summary", "type": "main", "index": 0}]]}, "Google Gemini Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Get Applicant Record": {"main": [[{"node": "Embed Resume in Notion", "type": "main", "index": 0}]]}, "Applicant Data Backup": {"main": [[{"node": "Create Applicant Record", "type": "main", "index": 0}]]}, "Upload Audio to Drive": {"main": [[{"node": "Link Audio in Notion", "type": "main", "index": 0}]]}, "Applicant Personal Data": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Application Form 1 of 3": {"main": [[{"node": "Upload CV", "type": "main", "index": 0}, {"node": "Application Data", "type": "main", "index": 0}]]}, "Application Form 2 of 3": {"main": [[{"node": "Upload CV", "type": "main", "index": 0}, {"node": "Application Data", "type": "main", "index": 0}]]}, "Application form 3 of 3": {"main": [[{"node": "Upload CV", "type": "main", "index": 0}, {"node": "Application Data", "type": "main", "index": 0}]]}, "Create Applicant Record": {"main": [[]]}, "Job Description Mapping": {"main": [[{"node": "HR Expert", "type": "main", "index": 0}]]}, "Job Description Summary": {"main": [[{"node": "Job Description Mapping", "type": "main", "index": 0}]]}, "Update_Applicant_Record": {"main": [[{"node": "Extract_Audio", "type": "main", "index": 0}]]}, "Applicant Qualifications": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "HR Expert", "type": "ai_outputParser", "index": 0}]]}}}