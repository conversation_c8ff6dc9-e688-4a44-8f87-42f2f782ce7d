<!-- 直接渲染内容，避免依赖视频加载和内联事件处理器 -->
<style>
  .range-container {
    position: relative;
    margin: 20px 0;
  }

  .range-input {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
  }

  .range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
  }

  .range-input::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: none;
  }

  .range-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
  }

  .current-value {
    font-weight: bold;
    color: #333;
    margin-left: 5px;
  }
</style>

<div class="form-group" style="margin-bottom:16px;">
  <label class="form-label" for="depth">
    Enter research depth (Default 1) <span class="current-value" id="depth-display">1</span>
  </label>
  <p style="font-size:12px;color:#666;text-align:left">
    This value determines how many sub-queries to generate.
  </p>
  <div class="range-container">
    <input
      class="range-input"
      type="range"
      id="depth"
      name="input-depth"
      value="1"
      step="1"
      max="5"
      min="1"
    >
    <div class="range-labels">
      <span>1</span>
      <span>2</span>
      <span>3</span>
      <span>4</span>
      <span>5</span>
    </div>
  </div>
  <!-- 隐藏的输入字段，用于确保正确的值传递 -->
  <input type="hidden" name="input-depth" id="depth-value" value="1">
</div>

<script>
// 确保range slider的值能正确传递给下游系统
document.addEventListener('DOMContentLoaded', function() {
  const depthSlider = document.getElementById('depth');
  const depthHidden = document.getElementById('depth-value');
  const depthDisplay = document.getElementById('depth-display');

  if (depthSlider && depthHidden) {
    // 初始化隐藏字段的值
    depthHidden.value = depthSlider.value;

    // 监听slider变化
    depthSlider.addEventListener('input', function() {
      depthHidden.value = this.value;
      if (depthDisplay) {
        depthDisplay.textContent = this.value;
      }
    });
  }
});
</script>
