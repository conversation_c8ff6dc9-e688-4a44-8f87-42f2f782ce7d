<!-- 直接渲染内容，避免依赖视频加载和内联事件处理器 -->
<div class="form-group" style="margin-bottom:16px;">
  <label class="form-label" for="depth">
    Enter research depth (Default 1)
  </label>
  <p style="font-size:12px;color:#666;text-align:left">
    This value determines how many sub-queries to generate.
  </p>
  <input
    class="form-input"
    type="range"
    id="depth"
    name="input-depth"
    value="1"
    step="1"
    max="5"
    min="1"
    list="depth-markers"
  >
  <datalist
    id="depth-markers"
    style="display: flex;
    flex-direction: row;
    justify-content: space-between;
    writing-mode: horizontal-tb;
    margin-top: -10px;
    text-align: center;
    font-size: 10px;
    margin-left: 16px;
    margin-right: 16px;"
  >
    <option value="1" label="1"></option>
    <option value="2" label="2"></option>
    <option value="3" label="3"></option>
    <option value="4" label="4"></option>
    <option value="5" label="5"></option>
  </datalist>
  <!-- 隐藏的输入字段，用于确保正确的值传递 -->
  <input type="hidden" name="input-depth" id="depth-value" value="1">
</div>

<script>
// 确保range slider的值能正确传递给下游系统
document.addEventListener('DOMContentLoaded', function() {
  const depthSlider = document.getElementById('depth');
  const depthHidden = document.getElementById('depth-value');

  if (depthSlider && depthHidden) {
    // 初始化隐藏字段的值
    depthHidden.value = depthSlider.value;

    // 监听slider变化
    depthSlider.addEventListener('input', function() {
      depthHidden.value = this.value;
    });
  }
});
</script>
