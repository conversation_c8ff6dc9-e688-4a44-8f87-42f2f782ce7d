<video
    style="display:none"
    src="/when_will_n8n_support_range_sliders.mp4"
    onerror='
  this.insertAdjacentHTML(`afterend`,
  `<div class="form-group" style="margin-bottom:16px;">
    <label class="form-label" for="breadth">
      Enter research depth (Default 1)
    </label>
    <p style="font-size:12px;color:#666;text-align:left">
      This value determines how many sub-queries to generate.
    </p>
    <input
      class="form-input"
      type="range"
      id="depth"
      name="field-1"
      value="1"
      step="1"
      max="5"
      min="1"
      list="depth-markers"
    >
    <datalist
      id="depth-markers"
      style="display: flex;
      flex-direction: row;
      justify-content: space-between;
      writing-mode: horizontal-tb;
      margin-top: -10px;
      text-align: center;
      font-size: 10px;
      margin-left: 16px;
      margin-right: 16px;"
    >
      <option value="1" label="1"></option>
      <option value="2" label="2"></option>
      <option value="3" label="3"></option>
      <option value="4" label="4"></option>
      <option value="5" label="5"></option>
    </datalist>
  </div>`)
  '
/>
