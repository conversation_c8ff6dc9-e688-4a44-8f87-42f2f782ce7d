{"id": "8hRFK0iEjVKmthLq", "meta": {"instanceId": "84ad02d6104594179f43f1ce9cfe3a81637b2faedb57dafcb9e649b7542988db", "templateCredsSetupCompleted": true}, "name": "CrunchBase Invester Data", "tags": [], "nodes": [{"id": "49130296-ad2e-43f6-8ec4-7f72161a3a1b", "name": "Daily Investor Data Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [0, 0], "parameters": {"rule": {"interval": [{"triggerAtHour": 9}]}}, "typeVersion": 1.2}, {"id": "24caa890-abba-435d-a385-18cdef03dfe2", "name": "Fetch Crunchbase Investor Data", "type": "n8n-nodes-base.httpRequest", "position": [220, 0], "parameters": {"url": "https://api.crunchbase.com/api/v4/searches/organizations", "method": "POST", "options": {}, "jsonBody": "{\n  \"field_ids\": [\n    \"identifier\",\n    \"name\",\n    \"short_description\",\n    \"location_identifiers\",\n    \"investment_stage\"\n  ],\n  \"query\": [\n    {\n      \"type\": \"predicate\",\n      \"field_id\": \"organization_types\",\n      \"operator_id\": \"includes\",\n      \"values\": [\n        \"investor\"\n      ]\n    }\n  ],\n  \"limit\": 5\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_CRUNCHBASE_API_KEY"}, {"name": "Authorization", "value": "Bearer YOUR_CRUNCHBASE_API_KEY"}]}}, "typeVersion": 4.2}, {"id": "4de0847b-6e11-457d-b04b-d03b4fb1e3c1", "name": "Extract Investor Fields", "type": "n8n-nodes-base.code", "position": [540, 0], "parameters": {"jsCode": "const entities = items[0].json.entities;\n\nreturn entities.map(entity => {\n  return {\n    json: {\n      name: entity.name,\n      short_description: entity.short_description,\n      location_identifiers: entity.location_identifiers,\n      investment_stage: entity.investment_stage\n    }\n  };\n});\n"}, "typeVersion": 2}, {"id": "a7d08f10-1fde-4e99-9127-bf12926d4197", "name": "Append to Investor Sheet", "type": "n8n-nodes-base.googleSheets", "position": [780, 0], "parameters": {"columns": {"value": {"Name": "={{ $json.name }}", "Location": "={{ $json.location_identifiers }}", "Investment Stage": "={{ $json.investment_stage }}", "Short description": "={{ $json.short_description }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Short description", "type": "string", "display": true, "required": false, "displayName": "Short description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Location", "type": "string", "display": true, "required": false, "displayName": "Location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Investment Stage", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Investment Stage", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1XOUvOgrhDdOorEj0-TL4EiVygMd1wpD3cktI2_bm-Ww/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1XOUvOgrhDdOorEj0-TL4EiVygMd1wpD3cktI2_bm-Ww", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1XOUvOgrhDdOorEj0-TL4EiVygMd1wpD3cktI2_bm-Ww/edit?usp=drivesdk", "cachedResultName": "CrunchBase Invester List"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "Rp7XiR3hxJfv03ZO", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "45c6eb29-4d24-49a6-b849-938b178b66c9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-40, -1700], "parameters": {"color": 5, "width": 420, "height": 1900, "content": "### 🔷 **SECTION 1: Getting the Data from Crunchbase**\n\n#### 🧩 Nodes:\n\n1. 🕒 **Daily Investor Data Trigger**\n2. 🌐 **Fetch Crunchbase Investor Data**\n\n---\n\n#### 🔍 What Happens in This Section?\n\nThis section is all about **automatically pulling investor data from Crunchbase** on a scheduled basis, without any manual effort.\n\n---\n\n#### 🕒 1. **Daily Investor Data Trigger**\n\n> *\"When should the automation run?\"*\n\n* This **Schedule Trigger** node starts your workflow automatically at your chosen time.\n* For example, it could run every day at 8 AM.\n* It removes the need to remember to check Crunchbase manually.\n\n✅ *Beginner Benefit:*\n\n> Even if you’re not technical, you just set the schedule and let n8n run this job every day while you sleep ☕📊\n\n---\n\n#### 🌐 2. **Fetch Crunchbase Investor Data**\n\n> *\"Where do we get the data from?\"*\n\n* This **HTTP Request** node sends a **POST request** to [Crunchbase’s API](https://data.crunchbase.com/docs).\n\n* It includes a **filter** for organization types like `investor` and requests fields like:\n\n  * `name`\n  * `short_description`\n  * `location_identifiers`\n  * `investment_stage`\n\n* Crunchbase replies with a **JSON** file full of matching investors.\n\n✅ *Beginner Benefit:*\n\n> You don’t need to open a browser or search anything. This step collects live investor data from Crunchbase and brings it into your workflow for you 🚀\n\n---\n\n### ✅ What This Section Gives You:\n\nA **live, up-to-date snapshot** of investor information, **automatically pulled** from Crunchbase, without coding or clicking.\n\n---\n\n"}, "typeVersion": 1}, {"id": "2dd2de36-770c-4833-884b-1525c1411d46", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [480, -1400], "parameters": {"color": 6, "width": 460, "height": 1600, "content": "### 🔷 **SECTION 2: Processing and Saving the Data**\n\n#### 🧩 Nodes:\n\n3. 🧮 **Extract Investor Fields**\n4. 📄 **Append to Investor Sheet**\n\n---\n\n#### 🔍 What Happens in This Section?\n\nNow that we have raw investor data, this section **cleans it up** and **stores it in a Google Sheet** for easy access and analysis.\n\n---\n\n#### 🧮 3. **Extract Investor Fields**\n\n> *\"Let’s clean and format the response.\"*\n\n* This **Code** node uses JavaScript to loop through the list of investors and extract just what you care about:\n\n  * ✅ Name\n  * 📝 Short description\n  * 🌍 Location\n  * 💰 Investment stage\n\n* Each investor becomes a **separate row-ready item** for Google Sheets.\n\n✅ *Beginner Benefit:*\n\n> You don’t need to look at messy data. This step reshapes it into a clean format automatically ✨\n\n---\n\n#### 📄 4. **Append to Investor Sheet**\n\n> *\"Store it somewhere useful.\"*\n\n* This **Google Sheets** node takes the cleaned data and **adds it to a selected sheet**.\n* It works like a magic assistant, typing the investor data into your spreadsheet row-by-row.\n* You now have a **running list** of investors to use for:\n\n  * 📈 Market research\n  * 🎯 Outreach planning\n  * 💡 Competitor benchmarking\n\n"}, "typeVersion": 1}, {"id": "75231f28-c911-4bdc-a6a2-c6255d293eb3", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-1820, -1680], "parameters": {"color": 4, "width": 1300, "height": 320, "content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\n"}, "typeVersion": 1}, {"id": "c8a8156f-946e-4a50-8789-aff9c66bd9b6", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1820, -1340], "parameters": {"color": 4, "width": 1289, "height": 3278, "content": "## ✨ n8n Workflow: Investor Data Automation from Crunchbase to Google Sheets\n\n---\n\n### 🔷 **SECTION 1: Getting the Data from Crunchbase**\n\n#### 🧩 Nodes:\n\n1. 🕒 **Daily Investor Data Trigger**\n2. 🌐 **Fetch Crunchbase Investor Data**\n\n---\n\n#### 🔍 What Happens in This Section?\n\nThis section is all about **automatically pulling investor data from Crunchbase** on a scheduled basis, without any manual effort.\n\n---\n\n#### 🕒 1. **Daily Investor Data Trigger**\n\n> *\"When should the automation run?\"*\n\n* This **Schedule Trigger** node starts your workflow automatically at your chosen time.\n* For example, it could run every day at 8 AM.\n* It removes the need to remember to check Crunchbase manually.\n\n✅ *Beginner Benefit:*\n\n> Even if you’re not technical, you just set the schedule and let n8n run this job every day while you sleep ☕📊\n\n---\n\n#### 🌐 2. **Fetch Crunchbase Investor Data**\n\n> *\"Where do we get the data from?\"*\n\n* This **HTTP Request** node sends a **POST request** to [Crunchbase’s API](https://data.crunchbase.com/docs).\n\n* It includes a **filter** for organization types like `investor` and requests fields like:\n\n  * `name`\n  * `short_description`\n  * `location_identifiers`\n  * `investment_stage`\n\n* Crunchbase replies with a **JSON** file full of matching investors.\n\n✅ *Beginner Benefit:*\n\n> You don’t need to open a browser or search anything. This step collects live investor data from Crunchbase and brings it into your workflow for you 🚀\n\n---\n\n### ✅ What This Section Gives You:\n\nA **live, up-to-date snapshot** of investor information, **automatically pulled** from Crunchbase, without coding or clicking.\n\n---\n\n<br/>\n\n---\n\n### 🔷 **SECTION 2: Processing and Saving the Data**\n\n#### 🧩 Nodes:\n\n3. 🧮 **Extract Investor Fields**\n4. 📄 **Append to Investor Sheet**\n\n---\n\n#### 🔍 What Happens in This Section?\n\nNow that we have raw investor data, this section **cleans it up** and **stores it in a Google Sheet** for easy access and analysis.\n\n---\n\n#### 🧮 3. **Extract Investor Fields**\n\n> *\"Let’s clean and format the response.\"*\n\n* This **Code** node uses JavaScript to loop through the list of investors and extract just what you care about:\n\n  * ✅ Name\n  * 📝 Short description\n  * 🌍 Location\n  * 💰 Investment stage\n\n* Each investor becomes a **separate row-ready item** for Google Sheets.\n\n✅ *Beginner Benefit:*\n\n> You don’t need to look at messy data. This step reshapes it into a clean format automatically ✨\n\n---\n\n#### 📄 4. **Append to Investor Sheet**\n\n> *\"Store it somewhere useful.\"*\n\n* This **Google Sheets** node takes the cleaned data and **adds it to a selected sheet**.\n* It works like a magic assistant, typing the investor data into your spreadsheet row-by-row.\n* You now have a **running list** of investors to use for:\n\n  * 📈 Market research\n  * 🎯 Outreach planning\n  * 💡 Competitor benchmarking\n\n✅ *Beginner Benefit:*\n\n> You don’t copy-paste anything — n8n writes investor data into your sheet for you. It’s like having a personal assistant with no salary 📊🤖\n\n---\n\n### ✅ What This Section Gives You:\n\nA fully automated process that **cleans up Crunchbase data** and **stores it neatly** in Google Sheets — ready for analysis, dashboards, or reporting.\n\n---\n\n### 🎁 Final Outcome\n\n✅ No coding needed\n✅ Runs on autopilot\n✅ Clean data in your sheet\n✅ Use for strategy, marketing, or VC tracking\n\n---\n\n\n"}, "typeVersion": 1}], "active": false, "pinData": {"Fetch Crunchbase Investor Data": [{"json": {"paging": {"next_page": "cursor_string", "total_items": 1250, "current_page": 1}, "entities": [{"name": "Sequoia Capital", "identifier": {"uuid": "12345", "permalink": "sequoia-capital"}, "investment_stage": ["early_stage", "seed"], "short_description": "Venture capital firm investing in early-stage companies.", "location_identifiers": ["united_states"]}, {"name": "<PERSON><PERSON><PERSON>", "identifier": {"uuid": "67890", "permalink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "investment_stage": ["seed", "growth_stage"], "short_description": "Venture capital firm in Silicon Valley.", "location_identifiers": ["united_states"]}]}}]}, "settings": {"executionOrder": "v1"}, "versionId": "daeaf695-d4ae-460d-84b1-b4cdba520d28", "connections": {"Extract Investor Fields": {"main": [[{"node": "Append to Investor Sheet", "type": "main", "index": 0}]]}, "Daily Investor Data Trigger": {"main": [[{"node": "Fetch Crunchbase Investor Data", "type": "main", "index": 0}]]}, "Fetch Crunchbase Investor Data": {"main": [[{"node": "Extract Investor Fields", "type": "main", "index": 0}]]}}}