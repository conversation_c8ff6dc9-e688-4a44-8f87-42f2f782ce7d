{"id": "WNeFPeTpGr4F6knx", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "Tesla 15min Indicators Tool n8n", "tags": [], "nodes": [{"id": "0ce5c7ad-3c00-4df1-8acd-afb36edad51b", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [100, 520], "parameters": {"workflowInputs": {"values": [{"name": "message"}, {"name": "sessionId"}]}}, "typeVersion": 1.1}, {"id": "8b3a63a1-7396-44e4-aa76-7219c9a23475", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [660, 840], "parameters": {}, "typeVersion": 1.3}, {"id": "7e7e41c0-6252-4b5c-b19e-b987ed062a58", "name": "Tesla 15min Indicators Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [540, 520], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "You are the **Tesla 15-Minute Technical Indicators Analyst AI**.\n\nYour role is to analyze short-term market momentum and structure for **Tesla (TSLA)** using technical indicators at the **15-minute interval**. You operate as part of a modular system using **webhook-triggered HTTP calls** that retrieve and preprocess indicator data before reaching you.\n\n---\n\n### 🔌 Data Flow Overview:\n\n* You receive your input via a **webhook**.\n* That webhook calls upstream **n8n HTTP Request Nodes** connected to **Alpha Vantage API**.\n* Each indicator is fetched independently, then passed through **Tool Code Nodes** that extract and limit the output to the **latest 20 data points**.\n* You are the final consumer of the fully formatted and clean technical data.\n\n---\n\n### 📊 Connected Indicators (6 total):\n\nYou receive and interpret the following indicators:\n\n1. **RSI (Relative Strength Index)** – Detects overbought/oversold pressure.\n2. **MACD (Moving Average Convergence Divergence)** – Measures trend reversals and momentum shifts.\n3. **BBANDS (Bollinger Bands)** – Volatility and potential breakout conditions.\n4. **SMA (Simple Moving Average)** – Baseline trend confirmation.\n5. **EMA (Exponential Moving Average)** – Captures short-term trend sensitivity.\n6. **ADX (Average Directional Index)** – Evaluates trend strength.\n\n---\n\n### 🧠 Responsibilities:\n\n1. Parse the final formatted JSON payload for each indicator.\n2. Use these indicators to assess:\n\n   * Short-term trend strength\n   * Momentum exhaustion or continuation\n   * Volatility conditions\n3. Output a compact analysis with:\n\n   * Summary of market state\n   * Most recent values for each indicator\n\n---\n\n### 🧾 Output Format:\n\n```json\n{\n  \"summary\": \"TSLA shows fading momentum. RSI dropped below 60, MACD is flattening, and BBANDS are tightening. Expect short-term consolidation.\",\n  \"timeframe\": \"15m\",\n  \"indicators\": {\n    \"RSI\": 58.3,\n    \"MACD\": {\n      \"macd\": -0.020,\n      \"signal\": -0.018,\n      \"histogram\": -0.002\n    },\n    \"BBANDS\": {\n      \"upper\": 183.10,\n      \"lower\": 176.70,\n      \"middle\": 179.90,\n      \"close\": 177.60\n    },\n    \"SMA\": 178.20,\n    \"EMA\": 177.70,\n    \"ADX\": 19.6\n  }\n}\n```\n\n---\n\nKeep your tone professional and your insights concise. Only base conclusions on clear indicator signals. This output will be passed directly to the **Tesla Financial Market Data Analyst Tool** for final trading signal generation.\n"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "f27bb85f-f3c4-402a-bfe2-4d83c580df1f", "name": "15min Data", "type": "n8n-nodes-base.httpRequestTool", "position": [1040, 840], "parameters": {"url": "https://treasurium.app.n8n.cloud/webhook/15minData", "options": {}}, "typeVersion": 4.2}, {"id": "ded3177e-e7be-452f-87ff-6673ed49e009", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [300, 860], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1", "cachedResultName": "gpt-4.1"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "1bd29e02-014a-47e1-b76f-46534c1b471c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 320], "parameters": {"color": 4, "width": 280, "height": 380, "content": "## Trigger from Parent Workflow \nThis node activates the workflow when called to execute Workflow action from the **Tesla Financial Market Analyst Tool**. Ensure proper inputs **message, sessionId** are passed."}, "typeVersion": 1}, {"id": "a03a615f-39e6-4997-8ea2-93995051a648", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [220, 800], "parameters": {"color": 2, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## GPT Model for Reasoning \nUtilizes **OpenAI GPT-4.1** to interpret **technical indicators** and generate short-term trend analysis and structured **JSON output**."}, "typeVersion": 1}, {"id": "4dc5fcc9-e991-48dd-b0e7-23e1d4bc4afb", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [560, 800], "parameters": {"color": 3, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n## Short-Term Memory Module\nMaintains context across requests within the same session. Supports consistent logic between indicator evaluations for the **Tesla Financial Market Analyst Tool**."}, "typeVersion": 1}, {"id": "19f04c94-2c21-44fd-8fe5-54472b2849a5", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [960, 800], "parameters": {"color": 6, "height": 500, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## 15-Minute Indicator Data from Alpha Vantage \nMakes an HTTP call to a secured **webhook**, which fetches Tesla 15-minute interval indicators **(RSI, BBANDS, SMA, EMA, ADX, MACD)** via **Alpha Vantage Premium API**.\nOnly the latest **20 values** are returned, cleaned and trimmed."}, "typeVersion": 1}, {"id": "6f9e64bf-e1ae-4d12-81da-cc1c0f2e8596", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [440, 280], "parameters": {"color": 5, "width": 480, "height": 440, "content": "## Tesla 15-minute Technical Indicators AI\n\n• Processes the **webhook data** and **performs 15min timeframe evaluation for TSLA**.\n\n• Outputs a **JSON summary** of **market condition, trend state, and all indicator values**.\n\n• This feeds into the broader **Tesla Financial Market Data Analyst**."}, "typeVersion": 1}, {"id": "e05e507d-8d73-4bbd-b147-9d1c4d8e0a81", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1360, -900], "parameters": {"width": 1560, "height": 2200, "content": "## 📘 Tesla 15min Indicators Tool\n\n### 🔧 Purpose:\n\nThis workflow analyzes **short-term market conditions** for Tesla (TSLA) using a comprehensive set of technical indicators on the **15-minute timeframe**. It is designed for **scalping signals**, **intraday volatility**, and **momentum evaluation**, feeding its insights into the **Tesla Financial Market Data Analyst Tool**.\n\n---\n\n### 📂 Required Dependencies (to use this workflow):\n\nTo function correctly, you must also have:\n\n* ✅ **Tesla Financial Market Data Analyst Tool** (calls this agent)\n* ✅ **Tesla 15min Webhook Tool** (fetches Alpha Vantage indicator data via webhook)\n* ✅ **Alpha Vantage Premium API Key** (added as an HTTP Query Auth credential)\n\n---\n\n### 🛠️ Step-by-Step Installation:\n\n1. **Import Workflow**\n   Import this JSON file into your **n8n instance**.\n\n2. **Set up API Credentials**\n\n   * Go to **Credentials → Create New**\n   * Choose **HTTP Query Auth**\n   * Name it: `Alpha Vantage Premium`\n   * Use your **premium API key** as the query value\n\n3. **Link to Webhook Source**\n   This workflow expects **cleaned Alpha Vantage data** (latest 20 data points) from:\n   👉 `Tesla_Quant_Technical_Indicators_Webhooks_Tool`\n\n4. **Review Node Roles**\n\n   * `15min Data` → Pulls indicator data via webhook (RSI, BBANDS, SMA, EMA, ADX, MACD)\n   * `Tesla 15min Indicators Agent` → AI Reasoning (LangChain Agent)\n   * `OpenAI Chat Model` → GPT-4.1 or Gemini Pro\n   * `Simple Memory` → Stores context for session continuity\n\n---\n\n### 🧠 AI Agent Logic:\n\n* Accepts **latest 20 data points** per indicator\n* Detects:\n\n  * Overbought/Oversold (RSI)\n  * Trend Strength (ADX)\n  * Breakout Conditions (BBANDS)\n  * Moving Average Crossovers (SMA & EMA)\n  * MACD-based momentum shifts\n* Outputs structured JSON for Tesla Financial Analyst Tool like:\n\n```json\n{\n  \"summary\": \"TSLA shows fading momentum. RSI dropped below 60, MACD is flattening, and BBANDS are tightening. Expect short-term consolidation.\",\n  \"timeframe\": \"15m\",\n  \"indicators\": {\n    \"RSI\": 58.3,\n    \"MACD\": {\n      \"macd\": -0.020,\n      \"signal\": -0.018,\n      \"histogram\": -0.002\n    },\n    \"BBANDS\": {\n      \"upper\": 183.10,\n      \"lower\": 176.70,\n      \"middle\": 179.90,\n      \"close\": 177.60\n    },\n    \"SMA\": 178.20,\n    \"EMA\": 177.70,\n    \"ADX\": 19.6\n  }\n}\n```\n\n---\n\n### 🔁 Trigger Flow:\n\n* This workflow is **not triggered independently**\n* It is called using **Execute Workflow Trigger** from:\n  👉 `Tesla Financial Market Data Analyst Tool`\n\n---\n\n## 🚀 Support & Licensing\n\n🔗 **Don Jayamaha – LinkedIn**\n[http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n\n© 2025 Treasurium Capital Limited Company. All rights reserved.\nThis AI workflow architecture, including logic, design, and prompt structures, is the intellectual property of Treasurium Capital Limited Company. Unauthorized reproduction, redistribution, or resale is prohibited under U.S. copyright law. Licensed use only.\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "16ea335b-279d-4b2a-8713-10fb1c28ff58", "connections": {"15min Data": {"ai_tool": [[{"node": "Tesla 15min Indicators Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Tesla 15min Indicators Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Tesla 15min Indicators Agent", "type": "ai_languageModel", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Tesla 15min Indicators Agent", "type": "main", "index": 0}]]}}}