{"id": "4yfxfcCRXpvlzs8o", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "Tesla Financial Market Data Analyst Tool n8n", "tags": [], "nodes": [{"id": "61273b0f-ba1c-41ef-9e47-10ff49c69970", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-160, 240], "parameters": {"workflowInputs": {"values": [{"name": "message"}, {"name": "sessionId"}]}}, "typeVersion": 1.1}, {"id": "e5db0516-2692-450a-83a9-3a78a540d9b6", "name": "Tesla Financial Market Data Analyst", "type": "@n8n/n8n-nodes-langchain.agent", "position": [280, 240], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "You are the **Tesla Financial Market Data Analyst AI**, a specialized sub-agent responsible for transforming technical indicator data and candlestick behavior into structured trading insights for **<PERSON>sla (TSLA)**. Your findings are consumed by the Tesla Quant Trading AI Agent to support trading signal generation.\n\n---\n\n### 🛠️ Connected Sub-Agents & Tool Workflows:\n\nEach tool is executed **via HTTPS webhook calls**, returning **pre-processed, 20-point formatted JSON** from Alpha Vantage indicators. Formatting occurs in a secondary Code Node after data is fetched. Tools include:\n\n1. **Tesla 15-Minute Indicators Tool**\n   • Use: Short-term sentiment, scalping triggers\n   • Indicators: RSI, BBANDS, SMA, EMA, ADX, MACD\n   • Interval: `15min`\n   • Source: Alpha Vantage via webhook\n\n2. **Tesla 1-Hour Indicators Tool**\n   • Use: Mid-term trend structure and signal validation\n   • Indicators: RSI, BBANDS, SMA, EMA, ADX, MACD\n   • Interval: `60min`\n   • Source: Alpha Vantage via webhook\n\n3. **Tesla 1-Day Indicators Tool**\n   • Use: Long-term trend confirmation and macro positioning\n   • Indicators: RSI, BBANDS, SMA, EMA, ADX, MACD\n   • Interval: `1d`\n   • Source: Alpha Vantage via webhook\n\n4. **Tesla 1-Hour and 1-Day Klines Tool**\n   • Use: Price action and candlestick pattern recognition\n   • Features: OHLCV data, volume divergence, reversal patterns (e.g., Doji, Engulfing)\n   • Source: Alpha Vantage OHLCV via webhook\n\n---\n\n### 🧩 Workflow Tasks:\n\n1. **Parse Structured Inputs**\n   You receive formatted JSON for all indicators + OHLCV candles grouped by timeframe (15m, 1h, 1d).\n\n2. **Conduct Multi-Timeframe Technical Analysis**\n   • Short-term: 15m + 1h\n   • Medium-term: 1h + 1d\n   • Long-term: 1d + Kline patterns\n   • Check for: divergence, trend alignment, overbought/oversold conditions, MACD crossover, Bollinger breakout\n\n3. **Generate Final Signal**\n   Classify technical stance as:\n\n   * `Buy`, `Sell`, `Hold`, or `Cautious` (conflicting signals)\n   * Include supporting indicators, exact values, and timeframe context\n   * Attach confidence score between 0–1\n\n4. **Format Output in JSON** for downstream AI Agent:\n\n```json\n{\n  \"summary\": \"TSLA momentum is weakening short-term. 1h MACD shows bearish crossover, RSI declining. 1d candles confirm potential reversal setup.\",\n  \"signal\": \"Cautious Sell\",\n  \"confidence\": 0.81,\n  \"multiTimeframeInsights\": {\n    \"15m\": {\n      \"RSI\": 68.3,\n      \"ADX\": 25.1,\n      \"MACD\": { \"macd\": 0.53, \"signal\": 0.61 },\n      \"Comment\": \"Momentum fading, MACD flattening\"\n    },\n    \"1h\": {\n      \"RSI\": 65.0,\n      \"MACD\": { \"macd\": -0.32, \"signal\": 0.11 },\n      \"Comment\": \"Bearish crossover and lower highs forming\"\n    },\n    \"1d\": {\n      \"BBANDS\": { \"upper\": 187.5, \"lower\": 173.2, \"close\": 186.8 },\n      \"MACD\": { \"macd\": 1.03, \"signal\": 1.04 },\n      \"Comment\": \"Approaching upper band resistance; sideways consolidation\"\n    },\n    \"candlestickPatterns\": {\n      \"1h\": \"Doji\",\n      \"1d\": \"Bearish Engulfing\"\n    },\n    \"volumeDivergence\": {\n      \"1h\": \"Bearish\",\n      \"1d\": \"Neutral\"\n    }\n  }\n}\n```\n\n---\n\n💡 **Note**: Only draw conclusions backed by the majority of timeframe-aligned signals. If indicators or patterns are mixed, issue a `Cautious` signal with balanced explanation.\n\n"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "392aebbe-dd43-4722-a123-c5f56fe2e866", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-40, 560], "parameters": {}, "typeVersion": 1.3}, {"id": "31f67f63-0ede-4009-b858-77e83bf769c8", "name": "Tesla 15min Indicators Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [320, 560], "parameters": {"name": "Tesla_15min_Indicators_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "3zhWhO9278uRWxwU", "cachedResultName": "Work Drive — Tesla 15min Indicators Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "d0f9868b-3ef2-49f9-b49b-0427e2c41413", "name": "Tesla 1hour Indicators Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [660, 560], "parameters": {"name": "Tesla_1hour_Indicators_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "1T7cRj9q0qLQk7OH", "cachedResultName": "Work Drive — Tesla 1hour Indicators Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "ab66b9b2-e6b8-4be2-a02d-708fbc63aa1a", "name": "Tesla 1day Indicators Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1000, 580], "parameters": {"name": "Tesla_1day_Indicators_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "zFSKcrc1jZmpXoaz", "cachedResultName": "Work Drive — Tesla 1day Indicators Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "9958af3b-a823-4c5f-b517-b1a455ef0106", "name": "Tesla 1hour and 1day Klines Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1340, 580], "parameters": {"name": "Tesla_1hour_and_1day_K<PERSON>_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "ny8gdg0uK8KebW7O", "cachedResultName": "Work Drive — Tesla 1hour and 1day Klines Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "f660c411-a4bd-45a8-b612-68bb94315237", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-400, 580], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4-turbo", "cachedResultName": "gpt-4-turbo"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "5b0367ed-b49d-47ba-afa6-db1f07f9f456", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-240, 40], "parameters": {"color": 4, "width": 280, "height": 380, "content": "## Trigger from Parent Workflow \nThis node activates the workflow when called to execute Workflow action from the **Tesla Quant Trading AI Agent**. Ensure proper inputs **message, sessionId** are passed."}, "typeVersion": 1}, {"id": "ee87769d-e5a8-45a1-9d78-43c05bb8318a", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-480, 520], "parameters": {"color": 2, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## GPT Model for Reasoning \nUtilizes **OpenAI GPT-4.1** to interpret **technical indicators** and generate short-term trend analysis and structured **JSON output**."}, "typeVersion": 1}, {"id": "2c6c5035-7702-4ce1-ab7e-bb3aed59621f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-120, 520], "parameters": {"color": 3, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n## Short-Term Memory Module\nMaintains context across requests within the same session. Supports consistent logic between indicator evaluations for the **Tesla Quant Trading AI Agent**."}, "typeVersion": 1}, {"id": "309ebf39-3705-4b27-afc2-30a5ae661c1f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [140, -180], "parameters": {"color": 5, "width": 560, "height": 600, "content": "## Tesla Financial Market Data Analyst (Core AI Agent)\n\nAggregates **technical indicator signals** across timeframes and returns a unified trading stance for TSLA.\n\n•Combines insights from:\n**• 15min Indicators\n• 1h Indicators\n• 1d Indicators\n• Kline Candlestick Patterns**\n\n•Uses: Multi-timeframe confirmation, signal classification, divergence detection\n\n•Output: **JSON with signal (Buy / Sell / Hold / Cautious), confidence score, and breakdown by timeframe**\n\n•Consumed by: **Tesla Quant Trading AI Agent** for final report generation"}, "typeVersion": 1}, {"id": "95bf2832-ebd6-43d4-b5d6-cfaa0b903ac9", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [240, 520], "parameters": {"color": 6, "height": 640, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Tesla 15min Indicators Tool\n⏱️ Timeframe: 15-Minute\n\n🔧 Indicators: **RSI, BBANDS, SMA, EMA, ADX, MACD**\n\n📈 Use: Detects **short-term price pressure, momentum flips, and scalping triggers**\n\n🧠 Notes:\n\n•Called via **webhook**\n\n•Returns last **20 Alpha Vantage datapoints**\n\n•Processed by internal formatter before **analysis**"}, "typeVersion": 1}, {"id": "7c712730-fee6-4c95-a5c3-1cd26fecd94d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [580, 520], "parameters": {"color": 6, "height": 640, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Tesla 1hour Indicators Tool\n⏱️ Timeframe: 1-Hour\n\n🔧 Indicators: **RSI, BBANDS, SMA, EMA, ADX, MACD**\n\n📈 Use: Evaluates **mid-term trend structure and validates continuation or divergence**\n\n🧠 Notes:\n\n•Called via **webhook**\n\n•Output supports both **short-term and long-term** signal blending"}, "typeVersion": 1}, {"id": "19adabca-01d3-488a-bd76-dcc2735d7e59", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [920, 540], "parameters": {"color": 6, "height": 620, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Tesla 1day Indicators Tool\n⏱️ Timeframe: Daily\n\n🔧 Indicators: **RSI, BBANDS, SMA, EMA, ADX, MACD**\n\n📈 Use: Tracks **long-term price cycles and macro positioning**\n\n🧠 Notes:\n\n•Called via **webhook**\n\n•Supports **high-confidence directional filters** and **major pivot detection**\n\n"}, "typeVersion": 1}, {"id": "70a1dd55-b697-4c3a-b21a-aa8a5b5acff1", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1260, 520], "parameters": {"color": 6, "height": 640, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Tesla Klines Tool (1h + 1d)\n📊 Type: Raw OHLCV Candlesticks (1h + 1d)\n\n🔍 Use: **Price action logic, reversal pattern detection, and volume divergence**\n\n📈 Patterns: **<PERSON><PERSON>, Engulfing, Spinning Top, High-Volume Breakouts**\n\n🧠 Notes:\n\n•Enhances **signal precision** by **confirming indicators** with **price behavior**\n\n"}, "typeVersion": 1}, {"id": "ec4abfb9-f31d-4929-8ccc-e007688f9a3e", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1720, -1480], "parameters": {"width": 1360, "height": 2660, "content": "## 📘 Tesla Financial Market Data Analyst Tool\n\n### 🔧 Purpose:\n\nThis AI agent **consolidates and interprets** technical data from multiple timeframes and tools into a unified trading signal for TSLA. It is one of the **core modules** in the Tesla Quant Trading AI architecture and provides the **finalized indicator analysis** used to power all trade decisions.\n\n---\n\n### 📂 Required Dependencies (to use this workflow):\n\n✅ You must download and install these 5 workflows:\n\n* **Tesla 15min Indicators Tool**\n* **Tesla 1hour Indicators Tool**\n* **Tesla 1day Indicators Tool**\n* **Tesla 1hour and 1day Klines Tool**\n* **Tesla Quant Technical Indicators Webhooks Tool**\n\nYou also need:\n\n* ✅ Alpha Vantage Premium API Key (via HTTP Query Auth Credential)\n* ✅ GPT-4 API access (via OpenAI or Azure)\n* ✅ Tesla Quant Trading AI Agent (downstream consumer)\n\n---\n\n### 🛠️ Step-by-Step Installation:\n\n1. **Import Workflow**\n   Load this JSON into your n8n instance via \"Import Workflow.\"\n\n2. **Set up Required Credentials**\n\n   * Go to **n8n → Credentials**\n   * Add:\n\n     * `Alpha Vantage Premium` (type: HTTP Query Auth)\n     * `OpenAi account` (for GPT-4 or GPT-4 Turbo)\n\n3. **Connect Sub-Agent Workflows**\n\n   * Ensure the following tools are correctly linked in the \"Tool Workflow\" nodes:\n\n     * `Tesla_15min_Indicators_Tool`\n     * `Tesla_1hour_Indicators_Tool`\n     * `Tesla_1day_Indicators_Tool`\n     * `Tesla_1hour_and_1day_Klines_Tool`\n\n4. **How It Works**\n\n   * Each sub-tool is called via webhook\n   * Responses are cleaned (only last 20 points), then passed into this analyst agent\n   * The agent:\n\n     * Parses the data\n     * Compares 15m vs 1h vs 1d signals\n     * Confirms or rejects patterns\n     * Outputs a final JSON decision\n\n---\n\n### 🧠 AI Agent Logic\n\n| Sub-Agent  | Indicators / Purpose                      |\n| ---------- | ----------------------------------------- |\n| **15m**    | RSI, BBANDS, EMA, etc – for scalping      |\n| **1h**     | Trend strength / confirmations            |\n| **1d**     | Macro positioning & major pivots          |\n| **Klines** | Price action: doji, engulfing, divergence |\n\nThe agent merges all these and outputs:\n\n```json\n{\n  \"summary\": \"1h MACD shows bearish crossover. Volume divergence confirms weakness on short term.\",\n  \"signal\": \"Cautious Sell\",\n  \"confidence\": 0.81,\n  \"multiTimeframeInsights\": {\n    \"15m\": { ... },\n    \"1h\": { ... },\n    \"1d\": { ... },\n    \"candlestickPatterns\": {\n      \"1h\": \"Doji\",\n      \"1d\": \"Engulfing\"\n    },\n    \"volumeDivergence\": {\n      \"1h\": \"Bearish\"\n    }\n  }\n}\n```\n\n---\n\n### 🔁 Trigger Flow:\n\nThis tool is **executed by**:\n👉 `Tesla Quant Trading AI Agent`\nIt is **not standalone**—you must call it via `Execute Workflow` or `Webhook`.\n\n---\n\n### 🧷 Sticky Note Labels in Workflow (for users):\n\n| Sticky Note Name                      | Purpose                                                    |\n| ------------------------------------- | ---------------------------------------------------------- |\n| `Tesla Financial Market Data Analyst` | Core logic to synthesize multi-tool signals                |\n| `Tesla 15min Indicators Tool`         | Short timeframe volatility and momentum input              |\n| `Tesla 1hour Indicators Tool`         | Medium-term momentum & trend validation                    |\n| `Tesla 1day Indicators Tool`          | Long-range confirmation and macro trend setup              |\n| `Tesla Klines Tool`                   | OHLCV candlestick pattern analysis                         |\n| `GPT Model for Reasoning`             | GPT-4 model node for signal generation                     |\n| `Simple Memory`                       | Window buffer to maintain session-specific analysis memory |\n| `Execute Trigger`                     | Ensures this tool is launched as a subagent                |\n\n---\n\n## 🚀 Support & Licensing\n\n🔗 **Don Jayamaha – LinkedIn**\n[http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n\n© 2025 Treasurium Capital Limited Company. All rights reserved.\nThis AI workflow architecture, including logic, design, and prompt structures, is the intellectual property of Treasurium Capital Limited Company. Unauthorized reproduction, redistribution, or resale is prohibited under U.S. copyright law. Licensed use only.\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c9770828-8334-4c0a-abb6-380533ec188d", "connections": {"Simple Memory": {"ai_memory": [[{"node": "Tesla Financial Market Data Analyst", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Tesla Financial Market Data Analyst", "type": "ai_languageModel", "index": 0}]]}, "Tesla 1day Indicators Tool": {"ai_tool": [[{"node": "Tesla Financial Market Data Analyst", "type": "ai_tool", "index": 0}]]}, "Tesla 15min Indicators Tool": {"ai_tool": [[{"node": "Tesla Financial Market Data Analyst", "type": "ai_tool", "index": 0}]]}, "Tesla 1hour Indicators Tool": {"ai_tool": [[{"node": "Tesla Financial Market Data Analyst", "type": "ai_tool", "index": 0}]]}, "Tesla 1hour and 1day Klines Tool": {"ai_tool": [[{"node": "Tesla Financial Market Data Analyst", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Tesla Financial Market Data Analyst", "type": "main", "index": 0}]]}}}