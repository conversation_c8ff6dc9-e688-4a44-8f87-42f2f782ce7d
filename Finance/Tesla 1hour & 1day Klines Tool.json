{"id": "iEfixTQ5cn9rjJCk", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "Tesla 1hour and 1day Klines Tool n8n", "tags": [], "nodes": [{"id": "65efda46-19c2-4d14-9893-84645c894978", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-160, 320], "parameters": {"workflowInputs": {"values": [{"name": "message"}, {"name": "sessionId"}]}}, "typeVersion": 1.1}, {"id": "7204ef90-e523-444d-b13d-5bf73ca11a45", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [120, 680], "parameters": {}, "typeVersion": 1.3}, {"id": "1696dad4-cbfc-4eb1-9764-8bf5ec423fbe", "name": "Tesla 1hour and 1day Klines Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [240, 320], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "You are a Tesla Candlestick Pattern and Volume Divergence Analyst AI.\n\nYour role is to analyze Tesla (TSLA) candlestick data across 1-hour and 1-day timeframes. Your goal is to identify key reversal signals, trend continuation structures, and volume-based anomalies to assist in mid- and long-term trading decisions.\n\nYou are connected to the following tools:\n1. **1-Hour Candlestick Data Tool** – Queries TSLA 60-minute OHLCV candles from Alpha Vantage.\n2. **1-Day Candlestick Data Tool** – Queries TSLA daily OHLCV candles from Alpha Vantage.\n\nYou also use:\n- Open ChatGPT (as your reasoning engine)\n- A short-term memory buffer\n- Upstream execution trigger from the Tesla Financial Market Analyst Tool\n\nYour tasks are:\n1. Parse the latest candles from both 1-hour and 1-day datasets.\n2. Detect potential **reversal candlestick patterns** such as:\n   - Hammer / Inverted Hammer\n   - Shooting Star\n   - Doji\n   - Bullish/Bearish Engulfing\n3. Identify any **volume divergence**:\n   - Bullish: Price decreasing but volume falling slower or rising\n   - Bearish: Price increasing while volume fades\n4. Output a structured analysis that can be passed to the Financial Market Analyst Tool\n\n### Output Format:\n\n```json\n{\n  \"summary\": \"Bearish signs detected on 1-day chart. A shooting star formed on high volume while RSI is elevated. Volume divergence seen on 1h chart as price rises but volume weakens.\",\n  \"candlestickPatterns\": {\n    \"1h\": \"None\",\n    \"1d\": \"Shooting Star\"\n  },\n  \"volumeDivergence\": {\n    \"1h\": \"Bearish\",\n    \"1d\": \"None\"\n  },\n  \"ohlcv\": {\n    \"1h\": {\n      \"close\": 174.1,\n      \"volume\": 1430000,\n      \"high\": 175.0,\n      \"low\": 173.8\n    },\n    \"1d\": {\n      \"close\": 188.3,\n      \"volume\": 21234000,\n      \"high\": 189.9,\n      \"low\": 183.7\n    }\n  }\n}"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "26d3cdab-95b0-41bc-8d64-6e0d42fb36a5", "name": "Candlestick Data Hour", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [440, 660], "parameters": {"url": "https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol=TSLA&interval=60min&outputsize=compact", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "toolDescription": "1-Hour Candles"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 1.1}, {"id": "751d452a-1a42-466a-9068-7f378f61994a", "name": "Candlestick Data Day", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [740, 680], "parameters": {"url": "https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=TSLA&outputsize=compact", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "toolDescription": "1-Day Candles"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 1.1}, {"id": "395840a9-294e-40dc-ad19-06baa23b9c8c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-180, 680], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1", "cachedResultName": "gpt-4.1"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "f2bcdbe8-3388-40f2-bf0f-d251c06e87ab", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-240, 120], "parameters": {"color": 4, "width": 280, "height": 380, "content": "## Trigger from Parent Workflow \nThis node activates the workflow when called to execute Workflow action from the **Tesla Financial Market Analyst Tool**. Ensure proper inputs **message, sessionId** are passed."}, "typeVersion": 1}, {"id": "16408ce0-3f62-4223-bb8d-9e768f49b009", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [40, 620], "parameters": {"color": 3, "height": 420, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Short-Term Memory Module\nMaintains **conversation context** and **prior state across requests**. This is useful for **multi-turn logic** or keeping track of **pattern analysis** across **timeframes**."}, "typeVersion": 1}, {"id": "b48c9a20-ba57-417f-a2d4-3e21d25c5e33", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [180, 100], "parameters": {"color": 5, "width": 400, "height": 400, "content": "## Tesla Klines Pattern & Volume AI \nThis AI agent processes candlestick data (1h and 1d) to detect:\n**• Reversal Patterns (e.g. <PERSON><PERSON>, Engulfing)**\n**• Volume Divergence (e.g. rising price with falling volume)**\nIt returns structured JSON for integration into the broader Tesla Quant system."}, "typeVersion": 1}, {"id": "0169c8c2-1eb1-440d-b165-c4cc90b4f92c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [360, 620], "parameters": {"color": 6, "height": 420, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## 1-Hour OHLCV Data from Alpha Vantage \nFetches **TSLA hourly candles** using **Alpha Vantage API**. Output is processed by the AI agent to detect **short-term patterns** and **volume signals**."}, "typeVersion": 1}, {"id": "ae9e8586-3aa0-4b36-ad06-8b0d9a35d188", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [660, 620], "parameters": {"color": 6, "height": 440, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## 1-Day OHLCV Data from Alpha Vantage \nPulls **TSLA daily candlestick data** **for macro-pattern recognition**. This feeds into **long-term reversal** and **divergence detection logic**."}, "typeVersion": 1}, {"id": "2655d097-987a-4f49-8aa2-8cc95fca6ce8", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-260, 620], "parameters": {"color": 2, "height": 400, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## GPT Model for Reasoning \nUses **OpenAI GPT (e.g. GPT-4.1)** to interpret **candlestick and volume data**, generate insights, and return **JSON-formatted pattern analysis**."}, "typeVersion": 1}, {"id": "66ce45b7-932a-4acc-b46d-9de679440aa3", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1040, -600], "parameters": {"width": 1060, "height": 1660, "content": "## 📘 Tesla 1hour and 1day Klines Tool\n\n### 🔧 Purpose:\n\nThis agent extracts and analyzes TSLA candlestick and volume data across **1-hour** and **1-day** intervals. It detects **reversal patterns** (like <PERSON>ji, Engulfing) and **volume divergence** signals to support mid- and long-term trading decisions.\n\n### 📂 Required Dependencies (to use this workflow):\n\nTo use this tool effectively, ensure the following are also downloaded and connected:\n\n* ✅ Tesla Financial Market Data Analyst Tool (calls this as a sub-agent)\n* ✅ Alpha Vantage Premium API key (attached via HTTP Query Auth Credential in n8n)\n\n---\n\n### 🛠️ Step-by-Step Installation:\n\n1. **Import Workflow**\n   Import this JSON into your n8n instance as a new workflow.\n\n2. **Set up Credentials**\n\n   * Go to “Credentials” in n8n and create a new **HTTP Query Auth** credential named:\n     👉 `Alpha Vantage Premium`\n   * Use your premium Alpha Vantage API key.\n\n3. **Link Triggers**\n\n   * This workflow starts with **\"Execute Workflow Trigger\"**, meaning it's **triggered by another parent workflow**, specifically:\n     👉 **Tesla Financial Market Data Analyst Tool**\n\n4. **Verify Node Functions**\n   Key nodes include:\n\n   * `Candlestick Data Hour`: fetches TSLA 1-hour OHLCV\n   * `Candlestick Data Day`: fetches TSLA 1-day OHLCV\n   * `Tesla Klines Agent`: GPT-powered reasoning agent analyzing candle patterns & volume divergence\n   * `OpenAI Chat Model`: Connects to GPT-4 (or GPT-4.1) for AI logic\n   * `Simple Memory`: stores short-term pattern context for use within a session\n\n5. **Test the Tool**\n   Once connected to its parent workflow, trigger a run and ensure a structured JSON like this is returned:\n\n   ```json\n   {\n     \"summary\": \"...\",\n     \"candlestickPatterns\": {\n       \"1h\": \"None\",\n       \"1d\": \"Doji\"\n     },\n     \"volumeDivergence\": {\n       \"1h\": \"Bearish\",\n       \"1d\": \"None\"\n     }\n   }\n   ```\n\n---\n\n### 🧠 AI Agent Logic:\n\n* Runs prompt logic via a `LangChain Agent` connected to OpenAI’s GPT-4.\n* Input: latest 1h and 1d OHLCV data from Alpha Vantage.\n* Output: detected reversal signals, pattern names, divergence flags, and summary.\n\n---\n\n## 🚀 Support & Licensing\n\n🔗 **Don Jayamaha – LinkedIn**\n[http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n\n© 2025 Treasurium Capital Limited Company. All rights reserved.\nThis AI workflow architecture, including logic, design, and prompt structures, is the intellectual property of Treasurium Capital Limited Company. Unauthorized reproduction, redistribution, or resale is prohibited under U.S. copyright law. Licensed use only.\n\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "dbf24a40-9c35-4395-bbc7-9c9e8870ac66", "connections": {"Simple Memory": {"ai_memory": [[{"node": "Tesla 1hour and 1day Klines Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Tesla 1hour and 1day Klines Agent", "type": "ai_languageModel", "index": 0}]]}, "Candlestick Data Day": {"ai_tool": [[{"node": "Tesla 1hour and 1day Klines Agent", "type": "ai_tool", "index": 0}]]}, "Candlestick Data Hour": {"ai_tool": [[{"node": "Tesla 1hour and 1day Klines Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Tesla 1hour and 1day Klines Agent", "type": "main", "index": 0}]]}}}