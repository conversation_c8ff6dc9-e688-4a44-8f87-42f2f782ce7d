{"id": "AkcTR8C45GvFGjg3", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6"}, "name": "Tesla Quant Technical Indicators Webhooks Tool n8n", "tags": [], "nodes": [{"id": "666ae1df-f6af-42f4-a9b9-9f065dadebf5", "name": "MACD 15min", "type": "n8n-nodes-base.httpRequest", "position": [520, -2100], "parameters": {"url": "https://www.alphavantage.co/query?function=MACD&symbol=TSLA&interval=15min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "09cdc5db-985b-4383-b207-5d3a4c4c7b43", "name": "RSI 15min", "type": "n8n-nodes-base.httpRequest", "position": [520, -1920], "parameters": {"url": "https://www.alphavantage.co/query?function=RSI&symbol=TSLA&interval=15min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "1900f4ee-8531-49c4-b993-c3189742631d", "name": "BBands 15min", "type": "n8n-nodes-base.httpRequest", "position": [520, -1760], "parameters": {"url": "https://www.alphavantage.co/query?function=BBANDS&symbol=TSLA&interval=15min&time_period=20&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "779b3201-bf41-45b7-b397-c7ad8da104e6", "name": "SMA 15min", "type": "n8n-nodes-base.httpRequest", "position": [520, -1560], "parameters": {"url": "https://www.alphavantage.co/query?function=SMA&symbol=TSLA&interval=15min&time_period=20&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "2ddff936-5d16-4ca3-a6e9-d2be4d1552b1", "name": "EMA 15min", "type": "n8n-nodes-base.httpRequest", "position": [520, -1380], "parameters": {"url": "https://www.alphavantage.co/query?function=EMA&symbol=TSLA&interval=15min&time_period=12&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "487ee540-198d-4067-a55c-d38cf9f8b5f9", "name": "ADX 15min", "type": "n8n-nodes-base.httpRequest", "position": [520, -1180], "parameters": {"url": "https://www.alphavantage.co/query?function=ADX&symbol=TSLA&interval=15min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "4b465062-273f-4e17-9af2-e3201c43912f", "name": "15min Data Webhook", "type": "n8n-nodes-base.webhook", "position": [60, -1660], "webhookId": "88e83c35-bba7-4ff8-a808-9626ae04b88c", "parameters": {"path": "88e83c35-bba7-4ff8-a808-9626ae04b88c", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "7bfdce8b-119f-4d4d-9957-336ce4c82fbb", "name": "RSI 1hour", "type": "n8n-nodes-base.httpRequest", "position": [-920, -460], "parameters": {"url": "https://www.alphavantage.co/query?function=RSI&symbol=TSLA&interval=60min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "5d4dbcb7-cd3b-48a0-a621-ccb5ab4002bb", "name": "MACD 1hour", "type": "n8n-nodes-base.httpRequest", "position": [-920, -660], "parameters": {"url": "https://www.alphavantage.co/query?function=MACD&symbol=TSLA&interval=60min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "e0c37a9d-e724-426f-a2a1-7ff938e480a2", "name": "BBands 1hour", "type": "n8n-nodes-base.httpRequest", "position": [-920, -260], "parameters": {"url": "https://www.alphavantage.co/query?function=BBANDS&symbol=TSLA&interval=60min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "fd9bc08f-9db4-404e-9e3d-68a56cef437f", "name": "SMA 1hour", "type": "n8n-nodes-base.httpRequest", "position": [-920, -40], "parameters": {"url": "https://www.alphavantage.co/query?function=SMA&symbol=TSLA&interval=60min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "31489239-8fc7-47e7-94d4-8a34c24c646a", "name": "EMA 1hour", "type": "n8n-nodes-base.httpRequest", "position": [-920, 160], "parameters": {"url": "https://www.alphavantage.co/query?function=EMA&symbol=TSLA&interval=60min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "35e97546-dc5d-4c30-9b6c-f6751a24c3a7", "name": "ADX 1hour", "type": "n8n-nodes-base.httpRequest", "position": [-920, 360], "parameters": {"url": "https://www.alphavantage.co/query?function=ADX&symbol=TSLA&interval=60min&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "655df5a9-0d91-41d1-bdb1-5be5cb97dce4", "name": "1hour Data Webhook", "type": "n8n-nodes-base.webhook", "position": [-1440, -120], "webhookId": "1eb54436-6511-4c6c-8d27-2a6ff8f15411", "parameters": {"path": "1eb54436-6511-4c6c-8d27-2a6ff8f15411", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "2b732a52-bfeb-486a-84ca-2ecffd6ff622", "name": "RSI 1day", "type": "n8n-nodes-base.httpRequest", "position": [2140, -580], "parameters": {"url": "https://www.alphavantage.co/query?function=RSI&symbol=TSLA&interval=daily&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "b456f6c4-bdb0-49b2-ba9e-999a3726f43b", "name": "MACD 1day", "type": "n8n-nodes-base.httpRequest", "position": [2140, -780], "parameters": {"url": "https://www.alphavantage.co/query?function=MACD&symbol=TSLA&interval=daily&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "a8e1a625-a49d-44bc-98e8-d1b3f4a57bfc", "name": "BBands 1day", "type": "n8n-nodes-base.httpRequest", "position": [2140, -360], "parameters": {"url": "https://www.alphavantage.co/query?function=BBANDS&symbol=TSLA&interval=daily&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "1ed45f01-19d5-4a1c-928a-b0f4456faece", "name": "SMA 1day", "type": "n8n-nodes-base.httpRequest", "position": [2140, -160], "parameters": {"url": "https://www.alphavantage.co/query?function=SMA&symbol=TSLA&interval=daily&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "2549b6e2-bb0e-433e-b137-f88fa21b39a9", "name": "EMA 1day", "type": "n8n-nodes-base.httpRequest", "position": [2140, 60], "parameters": {"url": "https://www.alphavantage.co/query?function=EMA&symbol=TSLA&interval=daily&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "18dd8937-5936-4915-a617-18629cc32eb8", "name": "ADX 1day", "type": "n8n-nodes-base.httpRequest", "position": [2140, 280], "parameters": {"url": "https://www.alphavantage.co/query?function=ADX&symbol=TSLA&interval=daily&time_period=14&series_type=close&entitlement=delayed", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "cDmyrlGDH8qhKXPx", "name": "Alpha Vantage Premium"}}, "typeVersion": 4.2}, {"id": "f593b208-aa5e-42fc-9f7b-b3abd6472de8", "name": "1day Data Webhook", "type": "n8n-nodes-base.webhook", "position": [1580, -200], "webhookId": "65f1d0f0-b165-45e7-9a99-ceb977a9abbf", "parameters": {"path": "65f1d0f0-b165-45e7-9a99-ceb977a9abbf", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "46cd5f07-970d-42a5-b300-2cd67515be4d", "name": "Respond to Webhook 1day Data", "type": "n8n-nodes-base.respondToWebhook", "position": [3360, -300], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1.2}, {"id": "86557d7b-7333-4189-9b06-5cbffd978c81", "name": "Respond to Webhook 15min Data", "type": "n8n-nodes-base.respondToWebhook", "position": [1740, -1660], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1.2}, {"id": "82c833de-4a12-4976-b998-129465e80804", "name": "Respond to Webhook 1hour Data", "type": "n8n-nodes-base.respondToWebhook", "position": [540, -120], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1.2}, {"id": "c9c62905-a5d2-4af7-bd7d-1d0310e34706", "name": "Merge 1hour Indicators", "type": "n8n-nodes-base.merge", "position": [100, -180], "parameters": {"numberInputs": 6}, "typeVersion": 3.1}, {"id": "4e2db262-5fbd-43f2-b0e8-5dfff74bd69b", "name": "Merge 15 min Indicators", "type": "n8n-nodes-base.merge", "position": [1420, -1720], "parameters": {"numberInputs": 6}, "typeVersion": 3.1}, {"id": "32bdda97-56b4-449d-8cc0-9e56f5cb33e7", "name": "Merge 1day Indicators", "type": "n8n-nodes-base.merge", "position": [3000, -360], "parameters": {"numberInputs": 6}, "typeVersion": 3.1}, {"id": "8c0d9209-786e-4ad8-b76b-0c94a3ffa788", "name": "Format Response - MACD 15min", "type": "n8n-nodes-base.code", "position": [940, -2100], "parameters": {"jsCode": "// Get the MACD data from input\nconst raw = items[0].json;\nconst resultKey = \"Technical Analysis: MACD\";\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: \"No MACD data returned.\" } }];\n}\n\n// Extract latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\nconst cleaned = Object.fromEntries(latest20);\n\nreturn [{\n  json: {\n    indicator: \"MACD\",\n    timeframe: \"15min\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "98fc07c6-d8ac-44a9-95d9-430485febca2", "name": "Format Response - RSI 15min", "type": "n8n-nodes-base.code", "position": [940, -1920], "parameters": {"jsCode": "// Extract RSI data from HTTP response\nconst raw = items[0].json;\nconst resultKey = \"Technical Analysis: RSI\";\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: \"No RSI data returned.\" } }];\n}\n\n// Trim to latest 20 points\nconst latest20 = Object.entries(data).slice(0, 20);\nconst cleaned = Object.fromEntries(latest20);\n\nreturn [{\n  json: {\n    indicator: \"RSI\",\n    timeframe: \"15min\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "2f7d7b00-ef80-4ee4-92e8-50c1707aed0e", "name": "Format Response - BBANDS 15min", "type": "n8n-nodes-base.code", "position": [940, -1760], "parameters": {"jsCode": "// Extract BBANDS data from HTTP response\nconst raw = items[0].json;\nconst resultKey = \"Technical Analysis: BBANDS\";\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: \"No BBANDS data returned.\" } }];\n}\n\n// Get the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\nconst cleaned = Object.fromEntries(latest20);\n\nreturn [{\n  json: {\n    indicator: \"BBANDS\",\n    timeframe: \"15min\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "7b214e3c-f725-48eb-98ec-799c60c08216", "name": "Format Response - SMA 15min", "type": "n8n-nodes-base.code", "position": [940, -1560], "parameters": {"jsCode": "// Extract SMA data from HTTP response\nconst raw = items[0].json;\nconst resultKey = \"Technical Analysis: SMA\";\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: \"No SMA data returned.\" } }];\n}\n\n// Get the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\nconst cleaned = Object.fromEntries(latest20);\n\nreturn [{\n  json: {\n    indicator: \"SMA\",\n    timeframe: \"15min\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "6f202147-927e-4427-8f99-06fdec6d614d", "name": "Format Response - EMA 15min", "type": "n8n-nodes-base.code", "position": [940, -1380], "parameters": {"jsCode": "// Extract EMA data from HTTP response\nconst raw = items[0].json;\nconst resultKey = \"Technical Analysis: EMA\";\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: \"No EMA data returned.\" } }];\n}\n\n// Get the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\nconst cleaned = Object.fromEntries(latest20);\n\nreturn [{\n  json: {\n    indicator: \"EMA\",\n    timeframe: \"15min\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "66147010-c52f-49ea-bca7-a668c38555bd", "name": "Format Response - ADX 15min", "type": "n8n-nodes-base.code", "position": [940, -1180], "parameters": {"jsCode": "// Extract ADX data from HTTP response\nconst raw = items[0].json;\nconst resultKey = \"Technical Analysis: ADX\";\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: \"No ADX data returned.\" } }];\n}\n\n// Get the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\nconst cleaned = Object.fromEntries(latest20);\n\nreturn [{\n  json: {\n    indicator: \"ADX\",\n    timeframe: \"15min\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "fe59f145-09fa-4dbc-9cf8-04b664e58259", "name": "Format Response - MACD 1hour", "type": "n8n-nodes-base.code", "position": [-500, -660], "parameters": {"jsCode": "// Extract MACD data from HTTP response\nconst raw = items[0].json;\nconst resultKey = \"Technical Analysis: MACD\";\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: \"No MACD data returned.\" } }];\n}\n\n// Get the latest 20 timestamp entries\nconst latest20 = Object.entries(data).slice(0, 20);\n\n// Transform into clean format\nconst cleaned = latest20.map(([timestamp, values]) => ({\n  timestamp,\n  macd: parseFloat(values.MACD),\n  signal: parseFloat(values.MACD_Signal),\n  hist: parseFloat(values.MACD_Hist)\n}));\n\nreturn [{\n  json: {\n    indicator: \"MACD\",\n    timeframe: \"1h\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "9e70c5aa-7b6a-4604-bf35-deaafcb31851", "name": "Format Response - RSI 1hour", "type": "n8n-nodes-base.code", "position": [-500, -460], "parameters": {"jsCode": "// Extract RSI data from HTTP response\nconst raw = items[0].json;\nconst resultKey = \"Technical Analysis: RSI\";\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: \"No RSI data returned.\" } }];\n}\n\n// Take latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\n\n// Format into simplified array\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  rsi: parseFloat(value.RSI)\n}));\n\nreturn [{\n  json: {\n    indicator: \"RSI\",\n    timeframe: \"1h\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "8647d17c-d2e7-4aaf-a94d-452a7830fe2a", "name": "Format Response - BBANDS 1hour", "type": "n8n-nodes-base.code", "position": [-500, -260], "parameters": {"jsCode": "const resultKey = 'Technical Analysis: BBANDS';\nconst raw = $json; // This captures the output of the HTTP node\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: `No BBANDS data returned from Alpha Vantage.` } }];\n}\n\n// Extract the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\n\n// Format each entry cleanly\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  upper: parseFloat(value['Real Upper Band']),\n  middle: parseFloat(value['Real Middle Band']),\n  lower: parseFloat(value['Real Lower Band']),\n}));\n\nreturn [{\n  json: {\n    indicator: 'BBANDS',\n    timeframe: '1h',\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "3f3a85aa-409c-4252-8e68-5e9cc1247b0b", "name": "Format Response - SMA 1hour", "type": "n8n-nodes-base.code", "position": [-500, -40], "parameters": {"jsCode": "const resultKey = 'Technical Analysis: SMA';\nconst raw = $json;\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: `No SMA data returned from Alpha Vantage.` } }];\n}\n\n// Extract the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\n\n// Format each entry cleanly\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  value: parseFloat(value['SMA']),\n}));\n\nreturn [{\n  json: {\n    indicator: 'SMA',\n    timeframe: '1h',\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "f6ae58f3-aad7-4d19-82d6-0e56339eb9ac", "name": "Format Response - EMA 1hour", "type": "n8n-nodes-base.code", "position": [-500, 160], "parameters": {"jsCode": "const resultKey = 'Technical Analysis: EMA';\nconst raw = $json;\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: `No EMA data returned from Alpha Vantage.` } }];\n}\n\n// Extract the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\n\n// Format each entry cleanly\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  value: parseFloat(value['EMA']),\n}));\n\nreturn [{\n  json: {\n    indicator: 'EMA',\n    timeframe: '1h',\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "67ff1f40-6e8f-4cd4-a67b-898687f56e54", "name": "Format Response - ADX 1hour", "type": "n8n-nodes-base.code", "position": [-500, 360], "parameters": {"jsCode": "const resultKey = 'Technical Analysis: ADX';\nconst raw = $json;\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: `No ADX data returned from Alpha Vantage.` } }];\n}\n\n// Extract the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\n\n// Format each entry cleanly\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  value: parseFloat(value['ADX']),\n}));\n\nreturn [{\n  json: {\n    indicator: 'ADX',\n    timeframe: '1h',\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "656ab57a-e8f6-48cc-8ebd-3ffe869eefbd", "name": "Format Response - MACD 1day", "type": "n8n-nodes-base.code", "position": [2520, -780], "parameters": {"jsCode": "const resultKey = 'Technical Analysis: MACD';\nconst raw = $json;\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: `No MACD data returned from Alpha Vantage.` } }];\n}\n\n// Extract the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\n\n// Format each entry cleanly\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  macd: parseFloat(value.MACD),\n  macdSignal: parseFloat(value.MACD_Signal),\n  macdHist: parseFloat(value.MACD_Hist),\n}));\n\nreturn [{\n  json: {\n    indicator: 'MACD',\n    timeframe: '1d',\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "eebab549-07fc-474c-86ae-bd39651cf3c3", "name": "Format Response - RSI 1day", "type": "n8n-nodes-base.code", "position": [2520, -580], "parameters": {"jsCode": "const resultKey = 'Technical Analysis: RSI';\nconst raw = $json;\nconst data = raw[resultKey];\n\nif (!data) {\n  return [{ json: { error: 'No RSI data returned from Alpha Vantage.' } }];\n}\n\n// Extract the latest 20 entries\nconst latest20 = Object.entries(data).slice(0, 20);\n\n// Format each entry\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  rsi: parseFloat(value.RSI),\n}));\n\nreturn [{\n  json: {\n    indicator: 'RSI',\n    timeframe: '1d',\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "66755c9a-c829-42ef-8b3a-3fa4df8d2a6e", "name": "Format Response - BBANDS 1day", "type": "n8n-nodes-base.code", "position": [2520, -360], "parameters": {"jsCode": "const raw = $json[\"Technical Analysis: BBANDS\"];\n\nif (!raw) {\n  return [{ json: { error: \"No BBANDS data returned from Alpha Vantage.\" } }];\n}\n\nconst latest20 = Object.entries(raw).slice(0, 20);\n\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  upper: parseFloat(value[\"Real Upper Band\"]) || null,\n  middle: parseFloat(value[\"Real Middle Band\"]) || null,\n  lower: parseFloat(value[\"Real Lower Band\"]) || null,\n}));\n\nreturn [{\n  json: {\n    indicator: \"BBANDS\",\n    timeframe: \"1d\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "2c21751d-a91c-4d35-bb26-9170e14c25aa", "name": "Format Response - SMA 1day", "type": "n8n-nodes-base.code", "position": [2520, -160], "parameters": {"jsCode": "const raw = $json[\"Technical Analysis: SMA\"];\n\nif (!raw) {\n  return [{ json: { error: \"No SMA data returned from Alpha Vantage.\" } }];\n}\n\n// Get the latest 20 data points\nconst latest20 = Object.entries(raw).slice(0, 20);\n\n// Clean and format each entry\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  sma: parseFloat(value.SMA) || null\n}));\n\nreturn [{\n  json: {\n    indicator: \"SMA\",\n    timeframe: \"1d\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "e50c1d0b-e5f0-4622-a350-1843c4fea180", "name": "Format Response - EMA 1day", "type": "n8n-nodes-base.code", "position": [2520, 60], "parameters": {"jsCode": "const raw = $json[\"Technical Analysis: EMA\"];\n\nif (!raw) {\n  return [{ json: { error: \"No EMA data returned from Alpha Vantage.\" } }];\n}\n\n// Extract and clean last 20 entries\nconst latest20 = Object.entries(raw).slice(0, 20);\n\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  ema: parseFloat(value.EMA) || null\n}));\n\nreturn [{\n  json: {\n    indicator: \"EMA\",\n    timeframe: \"1d\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "d620600d-1450-4aec-ac38-3bff733eacf6", "name": "Format Response - ADX 1day", "type": "n8n-nodes-base.code", "position": [2520, 280], "parameters": {"jsCode": "const raw = $json[\"Technical Analysis: ADX\"];\n\nif (!raw) {\n  return [{ json: { error: \"No ADX data returned from Alpha Vantage.\" } }];\n}\n\nconst latest20 = Object.entries(raw).slice(0, 20);\n\nconst cleaned = latest20.map(([timestamp, value]) => ({\n  timestamp,\n  adx: parseFloat(value.ADX) || null\n}));\n\nreturn [{\n  json: {\n    indicator: \"ADX\",\n    timeframe: \"1d\",\n    data: cleaned\n  }\n}];"}, "typeVersion": 2}, {"id": "7e58c620-1988-4ae7-89ca-55d7b201c842", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1500, -700], "parameters": {"color": 4, "height": 700, "content": "## Webhooks (1dayData)\n\n• **Entry point** for external workflows calling indicator batches\n\n• Orchestrates trigger for **indicator API pull + formatter nodes**\n\n• Returns **cleaned batch** back via Respond to Webhook node\n\n📎 Notes:\n\n• Must pass through correct **webhook** path\n\n• Responds after **merge with all 6 formatted outputs** per timeframe"}, "typeVersion": 1}, {"id": "37c8a591-5aa1-49b2-bd87-0efdbba4aa62", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, -2160], "parameters": {"color": 4, "height": 680, "content": "## Webhooks (15minData)\n\n• **Entry point** for external workflows calling indicator batches\n\n• Orchestrates trigger for **indicator API pull + formatter nodes**\n\n• Returns **cleaned batch** back via Respond to Webhook node\n\n📎 Notes:\n\n• Must pass through correct **webhook** path\n\n• Responds after **merge with all 6 formatted outputs** per timeframe"}, "typeVersion": 1}, {"id": "e2df7338-6357-4908-8c16-48dcfed20963", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1520, -620], "parameters": {"color": 4, "height": 680, "content": "## Webhooks (1hourData)\n\n• **Entry point** for external workflows calling indicator batches\n\n• Orchestrates trigger for **indicator API pull + formatter nodes**\n\n• Returns **cleaned batch** back via Respond to Webhook node\n\n📎 Notes:\n\n• Must pass through correct **webhook** path\n\n• Responds after **merge with all 6 formatted outputs** per timeframe"}, "typeVersion": 1}, {"id": "f4046d64-d579-4c19-b596-c2d535dddf4f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [460, -580], "parameters": {"color": 4, "width": 260, "height": 640, "content": "## Webhooks Respond (1hourData)\n\n• **Exit point** for external workflows calling indicator batches\n\n• Orchestrates trigger for **indicator API pull + formatter nodes**\n\n• Returns **cleaned batch** back via Respond to Webhook node\n\n📎 Notes:\n\n• Must pass through correct **webhook** path\n\n• Responds after **merge with all 6 formatted outputs** per timeframe"}, "typeVersion": 1}, {"id": "f4c94214-345e-45fc-acd4-8f3a691badf6", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1660, -2100], "parameters": {"color": 4, "width": 280, "height": 640, "content": "## Webhooks Respond (15minData)\n\n• **Exit point** for external workflows calling indicator batches\n\n• Orchestrates trigger for **indicator API pull + formatter nodes**\n\n• Returns **cleaned batch** back via Respond to Webhook node\n\n📎 Notes:\n\n• Must pass through correct **webhook** path\n\n• Responds after **merge with all 6 formatted outputs** per timeframe"}, "typeVersion": 1}, {"id": "4c1b8bef-f190-48e9-bc53-25638296ff77", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3280, -760], "parameters": {"color": 4, "width": 260, "height": 660, "content": "## Webhooks Respond (1dayData)\n\n• **Exit point** for external workflows calling indicator batches\n\n• Orchestrates trigger for **indicator API pull + formatter nodes**\n\n• Returns **cleaned batch** back via Respond to Webhook node\n\n📎 Notes:\n\n• Must pass through correct **webhook** path\n\n• Responds after **merge with all 6 formatted outputs** per timeframe"}, "typeVersion": 1}, {"id": "48955bf1-2028-41b0-95ce-b2295336fe7f", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2060, -1380], "parameters": {"color": 6, "width": 260, "height": 1900, "content": "## Alpha Vantage API Calls 1d \n\n• Triggers **Alpha Vantage Premium API** for 6 indicators:\n\n• **RSI, MACD, BBANDS, SMA, EMA, ADX**\n\n• Covers **1-day intervals for TSLA**\n\n• Called via dedicated **webhook endpoints** per timeframe\n\n• Data returned in **Alpha Vantage’s JSON schema**\n\n📎 Notes:\n\n• **Authentication** handled via **httpQueryAuth**\n\n• Each node pulls and returns **raw API data** for formatting"}, "typeVersion": 1}, {"id": "4d640d80-7359-4c9d-99a1-f1a3ec49aa73", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2440, -1380], "parameters": {"color": 2, "width": 260, "height": 1900, "content": "## Format Response Nodes (JSON Cleaners) \n\n• **Parses and extracts** the latest **20 data points per indicator**\n\n• Removes excess **metadata and reformats output as:\n json  { indicator, timeframe, data }**  \n\n• Ensures consistent **downstream format for merge and reasoning**\n\n📎 Notes:\n\n• **Format logic** is customized per indicator\n\n• Essential for **standardizing multi-timeframe data ingestion**"}, "typeVersion": 1}, {"id": "384390dd-7fb7-46d6-96de-b3cf1e94023b", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [440, -2680], "parameters": {"color": 6, "width": 260, "height": 1760, "content": "## Alpha Vantage API Calls 15m \n\n• Triggers **Alpha Vantage Premium API** for 6 indicators:\n\n• **RSI, MACD, BBANDS, SMA, EMA, ADX**\n\n• Covers **15-minute intervals for TSLA**\n\n• Called via dedicated **webhook endpoints** per timeframe\n\n• Data returned in **Alpha Vantage’s JSON schema**\n\n📎 Notes:\n\n• **Authentication** handled via **httpQueryAuth**\n\n• Each node pulls and returns **raw API data** for formatting"}, "typeVersion": 1}, {"id": "45e8abfd-1612-4569-843b-d0e5aab87815", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [860, -2680], "parameters": {"color": 2, "width": 260, "height": 1760, "content": "## Format Response Nodes (JSON Cleaners) \n\n• **Parses and extracts** the latest **20 data points per indicator**\n\n• Removes excess **metadata and reformats output as:\n json  { indicator, timeframe, data }**  \n\n• Ensures consistent **downstream format for merge and reasoning**\n\n📎 Notes:\n\n• **Format logic** is customized per indicator\n\n• Essential for **standardizing multi-timeframe data ingestion**"}, "typeVersion": 1}, {"id": "dba61599-047c-45e1-8dff-72d2fca3cd6b", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-580, -1260], "parameters": {"color": 2, "width": 260, "height": 1860, "content": "## Format Response Nodes (JSON Cleaners) \n\n• **Parses and extracts** the latest **20 data points per indicator**\n\n• Removes excess **metadata and reformats output as:\n json  { indicator, timeframe, data }**  \n\n• Ensures consistent **downstream format for merge and reasoning**\n\n📎 Notes:\n\n• **Format logic** is customized per indicator\n\n• Essential for **standardizing multi-timeframe data ingestion**"}, "typeVersion": 1}, {"id": "845c6359-0e9e-438f-bbdb-3566dee64c94", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -1260], "parameters": {"color": 6, "width": 260, "height": 1860, "content": "## Alpha Vantage API Calls 1h \n\n• Triggers **Alpha Vantage Premium API** for 6 indicators:\n\n• **RSI, MACD, BBANDS, SMA, EMA, ADX**\n\n• Covers **1-hour intervals for TSLA**\n\n• Called via dedicated **webhook endpoints** per timeframe\n\n• Data returned in **Alpha Vantage’s JSON schema**\n\n📎 Notes:\n\n• **Authentication** handled via **httpQueryAuth**\n\n• Each node pulls and returns **raw API data** for formatting"}, "typeVersion": 1}, {"id": "a55c9d0b-e45f-4012-8e59-8236a033f8cb", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [1340, -1980], "parameters": {"color": 5, "width": 260, "height": 620, "content": "## Technical Indicator API Merge \n\n**Alpha Vantage Premium API** for 6 indicators:\n\n• **RSI, MACD, BBANDS, SMA, EMA, ADX**"}, "typeVersion": 1}, {"id": "a74523af-432b-4ee9-8e40-8c5846c8f6af", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [20, -440], "parameters": {"color": 5, "width": 260, "height": 620, "content": "## Technical Indicator API Merge \n\n**Alpha Vantage Premium API** for 6 indicators:\n\n• **RSI, MACD, BBANDS, SMA, EMA, ADX**"}, "typeVersion": 1}, {"id": "634cf6ed-2231-4f99-bc1b-7dbe92d9a1d3", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2920, -660], "parameters": {"color": 5, "width": 260, "height": 660, "content": "## Technical Indicator API Merge \n\n**Alpha Vantage Premium API** for 6 indicators:\n\n• **RSI, MACD, BBANDS, SMA, EMA, ADX**"}, "typeVersion": 1}, {"id": "bf8ac533-da8a-4e8e-a3cb-5f89d8c85e1f", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [3700, -2000], "parameters": {"width": 1800, "height": 2520, "content": "## 📘 Tesla Quant Technical Indicators Webhooks Tool\n\n### 🔧 Purpose:\n\nThis workflow acts as the **central API data fetcher** for all Tesla technical indicators across **three timeframes**:\n👉 `15min`, `1hour`, and `1day`\n\nIt triggers and formats 6 Alpha Vantage indicators per timeframe and returns a clean, standardized JSON output used by downstream AI analyst tools.\n\n---\n\n### 🧱 Covered Indicators (per timeframe):\n\n* **RSI** – Relative Strength Index\n* **MACD** – Moving Average Convergence Divergence\n* **BBANDS** – Bollinger Bands\n* **SMA** – Simple Moving Average\n* **EMA** – Exponential Moving Average\n* **ADX** – Average Directional Index\n\n---\n\n### 📂 Required Dependencies (to use this workflow):\n\n* ✅ **Alpha Vantage Premium API Key** – Registered in n8n as a Credential\n* ✅ Any of the following parent workflows:\n\n  * Tesla Financial Market Data Analyst Tool\n  * Tesla 15min / 1h / 1d Indicators Tool\n\n---\n\n### 🛠️ Step-by-Step Installation:\n\n1. **Import Workflow**\n\n   * Load this JSON into your n8n instance as a standalone workflow.\n\n2. **Setup Alpha Vantage Credential**\n\n   * Go to **Credentials** in n8n.\n   * Add a new **HTTP Query Auth** credential named:\n     `Alpha Vantage Premium`\n   * Set key to `apikey` and add your actual premium Alpha Vantage token.\n\n3. **Connect to Caller**\n\n   * This tool exposes **3 webhook endpoints**:\n\n     * `/15minData`\n     * `/1hourData`\n     * `/1dayData`\n\n   These must be triggered by other workflows. When called, they:\n\n   * Pull all 6 indicators\n   * Format them into cleaned JSON (`indicator`, `timeframe`, `data`)\n   * Merge into a single object per timeframe\n   * Return via Respond to Webhook node\n\n4. **Verify Outputs**\n   Example response:\n\n   ```json\n   {\n     \"indicator\": \"RSI\",\n     \"timeframe\": \"1h\",\n     \"data\": {\n       \"2025-05-15 14:00\": \"58.42\",\n       ...\n     }\n   }\n   ```\n\n---\n\n### 🧠 Internal Architecture\n\nEach timeframe (15min, 1h, 1d) has:\n\n* ✅ 6 HTTP Request Nodes\n* ✅ 6 Formatter Code Nodes (latest 20 values)\n* ✅ Merge Node (combines results)\n* ✅ Webhook In Node (entry point)\n* ✅ Webhook Respond Node (exit to parent)\n\nSticky notes in n8n are standardized as:\n\n| Section         | Color  | Description                              |\n| --------------- | ------ | ---------------------------------------- |\n| Webhook Entry   | Blue   | Explains triggering path                 |\n| API Calls       | Yellow | Lists all HTTP indicator fetch nodes     |\n| Format Nodes    | Purple | Cleans & structures Alpha Vantage output |\n| Merge Nodes     | Green  | Final JSON merge of indicator array      |\n| Webhook Respond | Blue   | Returns JSON back to caller              |\n\n---\n\n### 🧩 Compatible Timeframes\n\n| Webhook Path | Used In                     |\n| ------------ | --------------------------- |\n| `/15minData` | Tesla 15min Indicators Tool |\n| `/1hourData` | Tesla 1hour Indicators Tool |\n| `/1dayData`  | Tesla 1day Indicators Tool  |\n\n---\n\n## 🚀 Support & Licensing\n\n🔗 **Don Jayamaha – LinkedIn**\n[http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n\n© 2025 Treasurium Capital Limited Company. All rights reserved.\nThis AI workflow architecture, including logic, design, and prompt structures, is the intellectual property of Treasurium Capital Limited Company. Unauthorized reproduction, redistribution, or resale is prohibited under U.S. copyright law. Licensed use only.\n\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5492ea23-51d6-4c57-bccf-786739f5f4b0", "connections": {"ADX 1day": {"main": [[{"node": "Format Response - ADX 1day", "type": "main", "index": 0}]]}, "EMA 1day": {"main": [[{"node": "Format Response - EMA 1day", "type": "main", "index": 0}]]}, "RSI 1day": {"main": [[{"node": "Format Response - RSI 1day", "type": "main", "index": 0}]]}, "SMA 1day": {"main": [[{"node": "Format Response - SMA 1day", "type": "main", "index": 0}]]}, "ADX 15min": {"main": [[{"node": "Format Response - ADX 15min", "type": "main", "index": 0}]]}, "ADX 1hour": {"main": [[{"node": "Format Response - ADX 1hour", "type": "main", "index": 0}]]}, "EMA 15min": {"main": [[{"node": "Format Response - EMA 15min", "type": "main", "index": 0}]]}, "EMA 1hour": {"main": [[{"node": "Format Response - EMA 1hour", "type": "main", "index": 0}]]}, "MACD 1day": {"main": [[{"node": "Format Response - MACD 1day", "type": "main", "index": 0}]]}, "RSI 15min": {"main": [[{"node": "Format Response - RSI 15min", "type": "main", "index": 0}]]}, "RSI 1hour": {"main": [[{"node": "Format Response - RSI 1hour", "type": "main", "index": 0}]]}, "SMA 15min": {"main": [[{"node": "Format Response - SMA 15min", "type": "main", "index": 0}]]}, "SMA 1hour": {"main": [[{"node": "Format Response - SMA 1hour", "type": "main", "index": 0}]]}, "MACD 15min": {"main": [[{"node": "Format Response - MACD 15min", "type": "main", "index": 0}]]}, "MACD 1hour": {"main": [[{"node": "Format Response - MACD 1hour", "type": "main", "index": 0}]]}, "BBands 1day": {"main": [[{"node": "Format Response - BBANDS 1day", "type": "main", "index": 0}]]}, "BBands 15min": {"main": [[{"node": "Format Response - BBANDS 15min", "type": "main", "index": 0}]]}, "BBands 1hour": {"main": [[{"node": "Format Response - BBANDS 1hour", "type": "main", "index": 0}]]}, "1day Data Webhook": {"main": [[{"node": "MACD 1day", "type": "main", "index": 0}, {"node": "RSI 1day", "type": "main", "index": 0}, {"node": "BBands 1day", "type": "main", "index": 0}, {"node": "SMA 1day", "type": "main", "index": 0}, {"node": "EMA 1day", "type": "main", "index": 0}, {"node": "ADX 1day", "type": "main", "index": 0}]]}, "15min Data Webhook": {"main": [[{"node": "MACD 15min", "type": "main", "index": 0}, {"node": "RSI 15min", "type": "main", "index": 0}, {"node": "BBands 15min", "type": "main", "index": 0}, {"node": "SMA 15min", "type": "main", "index": 0}, {"node": "EMA 15min", "type": "main", "index": 0}, {"node": "ADX 15min", "type": "main", "index": 0}]]}, "1hour Data Webhook": {"main": [[{"node": "MACD 1hour", "type": "main", "index": 0}, {"node": "RSI 1hour", "type": "main", "index": 0}, {"node": "BBands 1hour", "type": "main", "index": 0}, {"node": "SMA 1hour", "type": "main", "index": 0}, {"node": "EMA 1hour", "type": "main", "index": 0}, {"node": "ADX 1hour", "type": "main", "index": 0}]]}, "Merge 1day Indicators": {"main": [[{"node": "Respond to Webhook 1day Data", "type": "main", "index": 0}]]}, "Merge 1hour Indicators": {"main": [[{"node": "Respond to Webhook 1hour Data", "type": "main", "index": 0}]]}, "Merge 15 min Indicators": {"main": [[{"node": "Respond to Webhook 15min Data", "type": "main", "index": 0}]]}, "Format Response - ADX 1day": {"main": [[{"node": "Merge 1day Indicators", "type": "main", "index": 5}]]}, "Format Response - EMA 1day": {"main": [[{"node": "Merge 1day Indicators", "type": "main", "index": 4}]]}, "Format Response - RSI 1day": {"main": [[{"node": "Merge 1day Indicators", "type": "main", "index": 1}]]}, "Format Response - SMA 1day": {"main": [[{"node": "Merge 1day Indicators", "type": "main", "index": 3}]]}, "Format Response - ADX 15min": {"main": [[{"node": "Merge 15 min Indicators", "type": "main", "index": 5}]]}, "Format Response - ADX 1hour": {"main": [[{"node": "Merge 1hour Indicators", "type": "main", "index": 5}]]}, "Format Response - EMA 15min": {"main": [[{"node": "Merge 15 min Indicators", "type": "main", "index": 4}]]}, "Format Response - EMA 1hour": {"main": [[{"node": "Merge 1hour Indicators", "type": "main", "index": 4}]]}, "Format Response - MACD 1day": {"main": [[{"node": "Merge 1day Indicators", "type": "main", "index": 0}]]}, "Format Response - RSI 15min": {"main": [[{"node": "Merge 15 min Indicators", "type": "main", "index": 1}]]}, "Format Response - RSI 1hour": {"main": [[{"node": "Merge 1hour Indicators", "type": "main", "index": 1}]]}, "Format Response - SMA 15min": {"main": [[{"node": "Merge 15 min Indicators", "type": "main", "index": 3}]]}, "Format Response - SMA 1hour": {"main": [[{"node": "Merge 1hour Indicators", "type": "main", "index": 3}]]}, "Format Response - MACD 15min": {"main": [[{"node": "Merge 15 min Indicators", "type": "main", "index": 0}]]}, "Format Response - MACD 1hour": {"main": [[{"node": "Merge 1hour Indicators", "type": "main", "index": 0}]]}, "Format Response - BBANDS 1day": {"main": [[{"node": "Merge 1day Indicators", "type": "main", "index": 2}]]}, "Format Response - BBANDS 15min": {"main": [[{"node": "Merge 15 min Indicators", "type": "main", "index": 2}]]}, "Format Response - BBANDS 1hour": {"main": [[{"node": "Merge 1hour Indicators", "type": "main", "index": 2}]]}}}