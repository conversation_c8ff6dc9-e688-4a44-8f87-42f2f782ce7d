{"id": "vJw8YoQhdA5CXGeB", "meta": {"instanceId": "f2cf6301a998e16084609c8635767c7f2fe71603e7ba21b5457aa517ce5164cb", "templateCredsSetupCompleted": true}, "name": "Social_media_post _automation_from_google_trends_and _perplexity copy", "tags": [], "nodes": [{"id": "39b1272c-3700-4ce6-9aea-46b3a647fc26", "name": "X1", "type": "n8n-nodes-base.twitter", "position": [-1500, 0], "parameters": {"additionalFields": {}}, "credentials": {"twitterOAuth2Api": {"id": "tNwQsAkmjk3b0Dp6", "name": "X account"}}, "typeVersion": 2}, {"id": "4bab1fd3-1c21-4438-8cc1-e0d0fdde390e", "name": "Facebook Graph API1", "type": "n8n-nodes-base.facebookGraphApi", "position": [-1500, -400], "parameters": {"options": {}, "httpRequestMethod": "POST"}, "typeVersion": 1}, {"id": "ffcd9aa8-daef-41dc-8ad3-b9a55a817b8b", "name": "LinkedIn1", "type": "n8n-nodes-base.linkedIn", "position": [-1480, -200], "parameters": {"text": "={{ $json['choices[0].message.content'] }}", "person": "[CONFIGURE_YOUR_LINKEDIN_PERSON_ID]", "authentication": "communityManagement", "additionalFields": {}}, "credentials": {"linkedInCommunityManagementOAuth2Api": {"id": null, "name": "[CONFIGURE_YOUR_LINKEDIN_CREDENTIALS]"}}, "typeVersion": 1}, {"id": "a2c2c5e5-e9b1-4744-9883-c63cda3306c9", "name": "Google Sheets2", "type": "n8n-nodes-base.googleSheets", "position": [-1280, -200], "parameters": {"columns": {"value": {"Topic": "={{ $('Choosing Topic').item.json.message.content }}", "Status": "Posted", "AI Output": "={{ $json['choices[0].message.content'] }}", "Date Posted": "={{ $('Schedule Trigger').item.json.timestamp }}"}, "schema": [{"id": "Topic", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Topic", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "string", "display": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AI Output", "type": "string", "display": true, "required": false, "displayName": "AI Output", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date Posted", "type": "string", "display": true, "required": false, "displayName": "Date Posted", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Topic"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "[YOUR_GOOGLE_SHEETS_URL]", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "[YOUR_GOOGLE_SHEETS_DOCUMENT_ID]", "cachedResultUrl": "[YOUR_GOOGLE_SHEETS_URL]", "cachedResultName": "LinkedIn Automation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": null, "name": "[CONFIGURE_YOUR_GOOGLE_SHEETS_CREDENTIALS]"}}, "typeVersion": 4.5}, {"id": "51d3733a-004c-4938-9575-d227588140b7", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [-1720, -200], "parameters": {"options": {}, "fieldToSplitOut": "choices[0].message.content"}, "typeVersion": 1}, {"id": "cc9b1e41-aaeb-43fb-a283-e3faf397cc73", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-3420, -200], "parameters": {"rule": {"interval": [{"triggerAtHour": 6}, {"triggerAtHour": 18}]}}, "typeVersion": 1.2}, {"id": "01d0a3b7-66db-4032-b7d7-7a7b273920a7", "name": "Research Topic- Perplexity", "type": "n8n-nodes-base.httpRequest", "position": [-2160, -200], "parameters": {"url": "https://api.perplexity.ai/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"sonar-pro\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"Act as a skilled editor revising AI-generated text to sound authentically human. Follow these rules:\\n\\n1. Punctuation Adjustments\\n   - Replace em dashes, semicolons, or rephrase sentences where appropriate.\\n   - Avoid semicolons in casual contexts; use periods or conjunctions (e.g., 'and,' 'but').\\n   - Remove mid-sentence ellipses unless mimicking deliberate hesitation.\\n   - Limit parenthetical asides; integrate explanations into the main sentence.\\n   - Use colons sparingly.\\n\\n2. Language\\n   - Replace hedging phrases with direct statements.\\n   - Avoid stock transitions.\\n   - Vary repetitive terms.\\n   - Use contractions in informal contexts.\\n   - Replace overly formal words with simpler alternatives.\\n\\n3. Style\\n   - Prioritize concise, varied sentence lengths.\\n   - Allow minor imperfections.\\n   - Maintain the core message but adjust tone to match the audience.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"You are a researcher and expert copywriter in a business development team of an AI Automation Agency. Generate exactly one LinkedIn post that adheres to LinkedIn's API formatting guidelines and is easy for humans to read. Follow these rules:\\n\\nStructure:\\n1. Hook: Start with a bold opening line using Unicode characters (e.g., \\\"𝐁𝐎𝐋𝐃 𝐒𝐓𝐀𝐓𝐄𝐌𝐄𝐍𝐓\\\").\\n2. Body: Use short paragraphs (1-3 sentences) separated by \\\\n\\\\n.\\n3. Use bullet points (•) for key features or highlights.\\n4. End with a clear call-to-action (e.g., \\\"Comment below with your thoughts!\\\").\\n\\nFormatting Requirements:\\n- Remove all numeric citation brackets like [2], [3], [4] from the text.\\n- Instead of citation brackets, if needed, add a brief phrase like \\\"according to Google I/O 2025\\\" or \\\"as reported by DeepMind\\\" naturally within the sentence.\\n- No Markdown or rich text formatting.\\n- Use Unicode characters or emojis for emphasis (e.g., ★, 🚀).\\n- Include up to 3 relevant hashtags at the end (e.g., #AI #Automation).\\n- For URLs or references, use placeholders like [Link] instead of raw URLs.\\n- If mentioning users or companies, use official LinkedIn URN format (e.g., \\\"urn:li:organization:123456\\\").\\n\\nContent Rules:\\n- Max 1,200 characters.\\n- Avoid promotional language.\\n- Include one statistical claim or industry insight.\\n- Add one personal anecdote or professional observation.\\n\\nReturn the post as plain text without additional commentary.\\n\\nInput: {{ $json.message.content }}\"\n    }\n  ]\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": null, "name": "[CONFIGURE_YOUR_PERPLEXITY_API_KEY]"}}, "typeVersion": 4.2}, {"id": "1b226459-7dc7-46c2-9b02-769cd213e927", "name": "2 Most Trending", "type": "n8n-nodes-base.set", "position": [-2980, -200], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n  \"most-trending\": {\n\n    \"#1\": { \n\n      \"query\":\"{{ $json.related_queries.rising[0].query }}\",\n      \"score\":\"{{ $json.related_queries.rising[0].extracted_value }}\"\n\n    },\n\n\n    \"#2\": { \n\n      \"query\":\"{{ $json.related_queries.rising[1].query }}\",\n      \"score\":\"{{ $json.related_queries.rising[1].extracted_value }}\"\n\n    }\n  }\n}"}, "typeVersion": 3.4}, {"id": "75ccf42a-7dc8-4cc8-9d5d-202ff4b2b795", "name": "High search volume keywords", "type": "n8n-nodes-base.code", "position": [-2760, -200], "parameters": {"jsCode": "// Get the top array from your JSON\nconst topItems = $('Google Trends').first().json.related_queries.top;\n\n// Filter the items to only include those with extracted_value > 30\nconst filtered = topItems.filter(item => item.extracted_value > 30);\n\n// Map the filtered items to their query values and join them with commas\nconst resultString = filtered.map(item => item.query).join(', ');\n\n// Return the result in a new JSON property\nreturn [{ json: { result: resultString } }];\n"}, "typeVersion": 2}, {"id": "c85503d1-76e7-4dd7-a2e3-69966f69e0cd", "name": "Choosing <PERSON>ic", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-2540, -200], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo", "cachedResultName": "GPT-3.5-TURBO"}, "options": {}, "messages": {"values": [{"content": "=You are part of a team that creates world class blog posts. Your job is to choose the topic for each blog post. \n\nThe blog posts are posted on the website of a [COMPANY NAME + DESCRIPTION + PRODUCTS + TARGET MARKET]. The blog posts are mainly posted as part of an SEO campaign to get [YOUR_COMPANY] to rank high for its products and services.\n\nIn this instance, you are given a list of 2 keywords which have been trending the most on Google news search over the past few days. \n\nYour job is to pick one which you think would make for the most relevant blog post with the best SEO outcomes for the client. \n\nThe keywords have two attributes: \n\n1. query: This attribute determines the search query that users have been searching for which is trending. \n\n2. value: This attribute determines what percentage increase the keyword has seen compared to previous periods (i.e. the increase in search volume). \n\nYou must choose one out of the taking into consideration both the relevance of the keyword for [YOUR_COMPANY]'s SEO efforts and the comparative trendiness determined by the value attribute. \n\nOutput the keyword you decided to post a blog on and nothing else. Don't explain your reasoning. Just output the keyword. \n\nThis instance: \n\nKeyword 1:\n{{ $('2 Most Trending').item.json['most-trending']['#1'].toJsonString() }} \n\nKeyword2: \n{{ $('2 Most Trending').item.json['most-trending']['#2'].toJsonString() }} "}]}}, "credentials": {"openAiApi": {"id": null, "name": "[CONFIGURE_YOUR_OPENAI_API_KEY]"}}, "typeVersion": 1.8}, {"id": "37d7191c-878e-424e-90be-3fe2bdff625a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-3240, -280], "parameters": {"width": 400, "height": 240, "content": "## Find Trend\n\n"}, "typeVersion": 1}, {"id": "9987f2ae-0ec1-48b5-81f5-44a2f5a705b7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-2820, -280], "parameters": {"width": 200, "height": 240, "content": "## High Volume Keywords\n\n"}, "typeVersion": 1}, {"id": "f4a25815-10e6-409c-a89a-baa2efecb119", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-2580, -280], "parameters": {"width": 340, "height": 240, "content": "## Choosing Blog Topic\n\n\n"}, "typeVersion": 1}, {"id": "c9206a42-1471-4a87-99ad-25bd5d318fb4", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-2200, -280], "parameters": {"width": 600, "height": 240, "content": "## Research\n\n\n"}, "typeVersion": 1}, {"id": "089fdbf7-f1a0-4cd5-9769-caf94b05a939", "name": "Google Trends", "type": "n8n-nodes-base.httpRequest", "position": [-3200, -200], "parameters": {"url": "https://serpapi.com/search", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "=ai agents"}, {"name": "geo", "value": "US"}, {"name": "hl", "value": "en"}, {"name": "date", "value": "={{ $now.minus({ days: 3 }).format('yyyy-MM-dd') }} {{ $now.format('yyyy-MM-dd') }}"}, {"name": "data_type", "value": "RELATED_QUERIES"}, {"name": "engine", "value": "google_trends"}, {"name": "api_key", "value": "[YOUR_SERPAPI_KEY]"}]}}, "typeVersion": 4.2}, {"id": "13f0ea49-1dbe-4b33-9c73-ebe9d3099003", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [-1940, -200], "webhookId": "[GENERATED_WEBHOOK_ID]", "parameters": {"amount": 10}, "typeVersion": 1.1}, {"id": "c582949e-858f-4aa7-984b-0bd94212009f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1560, -540], "parameters": {"height": 800, "content": "## Multi Social Media Posting"}, "typeVersion": 1}, {"id": "77f592f4-fc6c-4633-ba2e-e671b18d6a1b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1300, -280], "parameters": {"height": 260, "content": "## Status update"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "9211dd42-49be-406b-94aa-297f48c13f3e", "connections": {"Wait": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "LinkedIn1": {"main": [[{"node": "Google Sheets2", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "X1", "type": "main", "index": 0}, {"node": "Facebook Graph API1", "type": "main", "index": 0}, {"node": "LinkedIn1", "type": "main", "index": 0}]]}, "Google Trends": {"main": [[{"node": "2 Most Trending", "type": "main", "index": 0}]]}, "Choosing Topic": {"main": [[{"node": "Research Topic- Perplexity", "type": "main", "index": 0}]]}, "2 Most Trending": {"main": [[{"node": "High search volume keywords", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Google Trends", "type": "main", "index": 0}]]}, "Research Topic- Perplexity": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "High search volume keywords": {"main": [[{"node": "Choosing <PERSON>ic", "type": "main", "index": 0}]]}}}