{"id": "ni6SfqC3kthAlPtX", "meta": {"instanceId": "a2eaba9e45ad7aab18b25cf863df1e910fb6dd3b85279bde97d9bae4a72f6862", "templateCredsSetupCompleted": true}, "name": "Personalized AI Tech Newsletter Using RSS, OpenAI and Gmail", "tags": [], "nodes": [{"id": "5cc6bfe1-dbaa-4196-ac52-27e3d5b7e91d", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [420, -300], "parameters": {}, "typeVersion": 1}, {"id": "c696de41-aeb1-4e2c-9e7e-8b04f7800bdb", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [960, -100], "parameters": {}, "typeVersion": 1.2}, {"id": "4b127a8f-14b3-4a0e-86f6-3157c59bc09c", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1120, -140], "parameters": {}, "typeVersion": 1}, {"id": "c32d87cd-28ee-4b28-ad53-43320169b6df", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1260, 0], "parameters": {}, "typeVersion": 1}, {"id": "c912148b-1142-4713-9769-1588ff308c62", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [400, 520], "parameters": {}, "typeVersion": 1.2}, {"id": "ba7aef6b-efec-4c35-a9d6-b2b8afb6b6c4", "name": "Get Articles Daily", "type": "n8n-nodes-base.scheduleTrigger", "position": [-20, -300], "parameters": {}, "typeVersion": 1.2}, {"id": "0827bf1b-1322-4e4a-8c5b-0da90382b202", "name": "Send Weekly Summary", "type": "n8n-nodes-base.scheduleTrigger", "position": [-40, 360], "parameters": {}, "typeVersion": 1.2}, {"id": "b1625ec0-fd2f-4098-ba79-1f522123cb86", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-100, -460], "parameters": {"content": ""}, "typeVersion": 1}, {"id": "7edbdba1-43ac-4754-91ae-d506ee38e8ff", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-100, 200], "parameters": {"content": ""}, "typeVersion": 1}, {"id": "e166715b-f579-4d22-bf2f-9318e4e86f2a", "name": "News reader AI", "type": "@n8n/n8n-nodes-langchain.agent", "position": [420, 300], "parameters": {}, "typeVersion": 1.9}, {"id": "c88c6c60-493e-41cf-b08d-3eef48e7cbc4", "name": "Send Newsletter", "type": "n8n-nodes-base.gmail", "position": [1020, 360], "webhookId": "0de8b6e8-8611-48a9-ba25-1d023698f577", "parameters": {}, "typeVersion": 2.1}, {"id": "8e303102-f68c-4cf8-a2bb-4538830610e6", "name": "Convert Response to an Email-Friendly Format", "type": "n8n-nodes-base.markdown", "position": [780, 300], "parameters": {}, "typeVersion": 1}, {"id": "3f90c79c-a04d-4537-b426-33900acfcb8a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [120, 300], "parameters": {"content": ""}, "typeVersion": 1}, {"id": "de315b7c-065c-45a7-be50-5d7a4eedeeaf", "name": "Your topics of interest", "type": "n8n-nodes-base.set", "position": [180, 360], "parameters": {}, "typeVersion": 3.4}, {"id": "8a1d6ac3-6fda-4916-a021-3d5db7d413e0", "name": "Store News Articles", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [1080, -300], "parameters": {}, "typeVersion": 1.1}, {"id": "b7fd5c59-3ed7-4706-bdd7-a62c62cd65af", "name": "Set Tech News RSS Feeds", "type": "n8n-nodes-base.set", "position": [200, -300], "parameters": {}, "typeVersion": 3.4}, {"id": "77f5f3bc-8ecd-481a-a570-6e49e4fda01b", "name": "Read RSS News Feeds", "type": "n8n-nodes-base.rssFeedRead", "position": [640, -300], "parameters": {}, "typeVersion": 1.1}, {"id": "540f55b3-10d1-4f7e-bbdf-793ae6524fd7", "name": "Get News Articles", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [540, 500], "parameters": {}, "typeVersion": 1.1}, {"id": "f5e37288-ef4c-41ea-87bd-1e9ee1e9ab0f", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [740, 620], "parameters": {}, "typeVersion": 1.2}, {"id": "f6e050de-8dc1-41dd-a18f-225a2f5f68ad", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [140, -360], "parameters": {"content": ""}, "typeVersion": 1}, {"id": "4d773ce7-cbca-4568-bd40-0f9914e835bb", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-580, -460], "parameters": {"content": ""}, "typeVersion": 1}, {"id": "796c2a13-c168-4bc9-b79b-fc80c31274c1", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [900, 580], "parameters": {"content": ""}, "typeVersion": 1}, {"id": "4bd264b8-b088-413d-b44b-dae3b7cc3e77", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [960, 300], "parameters": {"content": ""}, "typeVersion": 1}, {"id": "6d2b402d-22e0-4cc5-a070-8b4169f18a99", "name": "Set and Normalize Fields", "type": "n8n-nodes-base.set", "position": [860, -300], "parameters": {}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "9b875084-83f0-43e6-968a-caec8fb29d7b", "connections": {"Split Out": {"main": [[{"node": "Read RSS News Feeds", "type": "main", "index": 0}]]}, "News reader AI": {"main": [[{"node": "Convert Response to an Email-Friendly Format", "type": "main", "index": 0}]]}, "Send Newsletter": {"main": [[]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Store News Articles", "type": "ai_embedding", "index": 0}]]}, "Get News Articles": {"ai_tool": [[{"node": "News reader AI", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "News reader AI", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Get News Articles", "type": "ai_embedding", "index": 0}]]}, "Get Articles Daily": {"main": [[{"node": "Set Tech News RSS Feeds", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Store News Articles", "type": "ai_document", "index": 0}]]}, "Read RSS News Feeds": {"main": [[{"node": "Set and Normalize Fields", "type": "main", "index": 0}]]}, "Send Weekly Summary": {"main": [[{"node": "Your topics of interest", "type": "main", "index": 0}]]}, "Set Tech News RSS Feeds": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Your topics of interest": {"main": [[{"node": "News reader AI", "type": "main", "index": 0}]]}, "Set and Normalize Fields": {"main": [[{"node": "Store News Articles", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Convert Response to an Email-Friendly Format": {"main": [[{"node": "Send Newsletter", "type": "main", "index": 0}]]}}}