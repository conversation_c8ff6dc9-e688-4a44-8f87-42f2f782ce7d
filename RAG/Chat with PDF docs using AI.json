{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "e2e61eae-6306-47db-908c-9d82758f6516", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-660, 40], "parameters": {}, "typeVersion": 1}, {"id": "a45afcc0-d780-462a-9ed7-27daf01363a7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-500, -140], "parameters": {"color": 7, "width": 1086.039382705461, "height": 728.4168721167887, "content": "## 1. Setup: Fetch file from Google Drive, split it into chunks and insert into a vector database\nNote that running this part multiple times will insert multiple copies into your DB"}, "typeVersion": 1}, {"id": "a3c56569-0728-4246-8d87-fa106d373566", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-960, -60], "parameters": {"height": 350.7942096493649, "content": "# Try me out\n1. In Pinecone, create an index with 1536 dimensions and select it in the two vector store nodes\n2. Populate Pinecone by clicking the 'test workflow' button below\n3. Click the 'chat' button below and enter the following:\n\n_Which email provider does the creator of Bitcoin use?_"}, "typeVersion": 1}, {"id": "c1543b8a-dbea-42a9-a35e-e22ed86f565b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-500, 640], "parameters": {"color": 7, "width": 1594, "height": 529, "content": "## 2. <PERSON><PERSON> with file, getting citations in reponse"}, "typeVersion": 1}, {"id": "5300d5dd-4186-4402-9442-88adab4e9a89", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-480, -40], "parameters": {"color": 7, "width": 179.**************, "height": 257.**************, "content": "Will fetch the Bitcoin whitepaper, but you can change this"}, "typeVersion": 1}, {"id": "9f707f2b-6cb2-47b8-88fc-65cfd09b6cae", "name": "Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [80, 40], "parameters": {"mode": "insert", "options": {}, "pineconeIndex": {"__rl": true, "mode": "id", "value": "test-index"}}, "credentials": {"pineconeApi": {"id": "OHDlDbBkaPDgpnOY", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "a32ac59e-efdc-4ff3-92dd-be794c2be7f7", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-660, 760], "webhookId": "cd2703a7-f912-46fe-8787-3fb83ea116ab", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "e14145d2-0c18-4813-9555-263314cb0376", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [340, 980], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "e6863abd-d3df-4b45-9083-96b82cd46773", "name": "Set file URL in Google Drive", "type": "n8n-nodes-base.set", "position": [-440, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dc7a70e3-9b04-404b-8892-ba0fcc4274c2", "name": "file_url", "type": "string", "value": " https://drive.google.com/file/d/11Koq9q53nkk0F5Y8eZgaWJUVR03I4-MM/view"}]}}, "typeVersion": 3.4}, {"id": "80d241f1-7c8a-489e-9255-84bc79ec11c7", "name": "Download file", "type": "n8n-nodes-base.googleDrive", "position": [-220, 40], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "={{ $json.file_url }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "8483b283-1ff4-4540-891a-09886c146e16", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [180, 240], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "file_url", "value": "={{ $('Set file URL in Google Drive').first().json.file_url }}"}, {"name": "file_name", "value": "={{ $('Download file').first().binary.data.fileName }}"}]}}, "dataType": "binary"}, "typeVersion": 1}, {"id": "c262df34-b2d9-4f48-b975-d694469e6e5a", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-220, 980], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "45c8e8cb-a29e-48ad-985f-e0136065840f", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [40, 240], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "8c852568-f100-4849-a06f-86e71733512a", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [260, 400], "parameters": {"options": {}, "chunkSize": 3000, "chunkOverlap": 200}, "typeVersion": 1}, {"id": "319e5b2d-c648-4ef5-8238-7732c62d34f5", "name": "Set max chunks to send to model", "type": "n8n-nodes-base.set", "position": [-420, 760], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "33f4addf-72f3-4618-a6ba-5b762257d723", "name": "chunks", "type": "number", "value": 4}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "91b9132e-ef51-4044-be1b-f391aeeb467c", "name": "Get top chunks matching query", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [-220, 760], "parameters": {"mode": "load", "topK": "={{ $json.chunks }}", "prompt": "={{ $json.chatInput }}", "options": {}, "pineconeIndex": {"__rl": true, "mode": "id", "value": "test-index"}}, "credentials": {"pineconeApi": {"id": "OHDlDbBkaPDgpnOY", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "5ad6e0fd-c296-4507-8232-164b5be57f4a", "name": "Prepare chunks", "type": "n8n-nodes-base.code", "position": [140, 760], "parameters": {"jsCode": "let out = \"\"\nfor (const i in $input.all()) {\n  let itemText = \"--- CHUNK \" + i + \" ---\\n\"\n  itemText += $input.all()[i].json.document.pageContent + \"\\n\"\n  itemText += \"\\n\"\n  out += itemText\n}\n\nreturn {\n  'context': out\n};"}, "typeVersion": 2}, {"id": "770b066a-abb2-443e-bcaa-14632c6696f4", "name": "Answer the query based on chunks", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [340, 760], "parameters": {"text": "={{ $json.context }}\n\nQuestion: {{ $('When chat message received').first().json.chatInput }}\nHelpful Answer:", "options": {"systemPromptTemplate": "=Use the following pieces of context to answer the question at the end. If you don't know the answer, just say that you don't know, don't try to make up an answer. Important: In your response, also include the the indexes of the chunks you used to generate the answer."}, "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"required\": [\"answer\", \"citations\"],\n  \"properties\": {\n    \"answer\": {\n      \"type\": \"string\"\n    },\n    \"citations\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"number\"\n      }\n    }\n  }\n}"}, "typeVersion": 1}, {"id": "e43abc0c-cedf-4e73-a766-7fad57601cfe", "name": "Compose citations", "type": "n8n-nodes-base.set", "position": [700, 760], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ace6185e-8b3d-4f89-ae36-dfe0c391a0a9", "name": "citations", "type": "array", "value": "={{ $json.citations.map(i => '[' + $('Get top chunks matching query').all()[$json.citations].json.document.metadata.file_name + ', lines ' + $('Get top chunks matching query').all()[$json.citations].json.document.metadata['loc.lines.from'] + '-' + $('Get top chunks matching query').all()[$json.citations].json.document.metadata['loc.lines.to'] + ']') }}"}]}}, "typeVersion": 3.4}, {"id": "f82df340-42fc-4e92-9e9d-d808f19e0407", "name": "Generate response", "type": "n8n-nodes-base.set", "position": [900, 760], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "11396286-0378-4c3a-86e1-c9ef51afbfc7", "name": "text", "type": "string", "value": "={{ $json.answer }} {{ $if(!$json.citations.isEmpty(), \"\\n\" + $json.citations.join(\"\"), '') }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Download file": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Prepare chunks": {"main": [[{"node": "Answer the query based on chunks", "type": "main", "index": 0}]]}, "Compose citations": {"main": [[{"node": "Generate response", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Get top chunks matching query", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Answer the query based on chunks", "type": "ai_languageModel", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Set max chunks to send to model", "type": "main", "index": 0}]]}, "Set file URL in Google Drive": {"main": [[{"node": "Download file", "type": "main", "index": 0}]]}, "Get top chunks matching query": {"main": [[{"node": "Prepare chunks", "type": "main", "index": 0}]]}, "Set max chunks to send to model": {"main": [[{"node": "Get top chunks matching query", "type": "main", "index": 0}]]}, "Answer the query based on chunks": {"main": [[{"node": "Compose citations", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Set file URL in Google Drive", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}}}