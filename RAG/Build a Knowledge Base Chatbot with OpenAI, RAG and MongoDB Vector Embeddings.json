{"meta": {"instanceId": "074f90e2bb5206c5f405a8aac6551497c72005283a5405fb08207b1b3a78c2b8", "templateCredsSetupCompleted": true}, "nodes": [{"id": "5cb0a836-f9a1-4f92-9326-cd82a392d0da", "name": "Knowledge Base Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [220, 0], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are the AI assistant for an internal support team at a technology company specializing in advanced software solutions. Your task is to assist internal users by consulting the official product documentation stored in the company’s knowledge base.\n\nAvailable references:\n\nproductDocs: Step-by-step guides, technical configurations, and official manuals extracted from the product’s documentation.\n\nBehavior rules for answering questions:\nAlways consult the official product documentation first using the productDocs tool.\n\nRespond clearly and directly, explaining how to do what is requested.\n\nDo not filter by category unless explicitly asked by the user.\n\nDetect the language of each incoming message individually and respond in that language. Do not use prior conversation language or history to decide the response language.\n\nNever provide links, even if requested. If a user asks for a link, reply:\n“I cannot provide links. If you need specific information, please let me know and I will help with the details.”\n\nUse a professional, direct, and human tone.\n\nKeep answers between 2 and 4 lines unless the user requests more detail.\n\nDo not invent information that is not in the knowledge base.\n\nIf you give numbered steps or lists, number them sequentially (1, 2, 3...) without skipping or repeating numbers, even if the source content uses different numbering."}, "promptType": "define"}, "typeVersion": 1.9}, {"id": "56e6fb75-6a97-4466-9e7f-70710c2740d7", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [60, 240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "cJRah9hGPQ7eX4jd", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "e352c32e-7108-4a0d-b081-b2532d96d092", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [680, 380], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "cJRah9hGPQ7eX4jd", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "74bbfb00-1a00-4131-a291-bce5b79628b4", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-60, -420], "parameters": {}, "typeVersion": 1}, {"id": "f720a4b0-6239-4a0b-bb61-1e43f78f8e40", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [320, 220], "parameters": {}, "typeVersion": 1.3}, {"id": "94561d61-4a01-48b6-b114-dc4d47546ff3", "name": "MongoDB Vector Search", "type": "@n8n/n8n-nodes-langchain.vectorStoreMongoDBAtlas", "position": [560, 220], "parameters": {"mode": "retrieve-as-tool", "options": {}, "toolName": "productDocs", "mongoCollection": {"__rl": true, "mode": "list", "value": "n8n-template", "cachedResultName": "n8n-template"}, "toolDescription": "retreive documentation", "vectorIndexName": "data_index"}, "credentials": {"mongoDb": {"id": "7riubYENUDZsmjyK", "name": "MongoDB account 2"}}, "typeVersion": 1.1}, {"id": "c473c33d-5681-4f3a-ac36-0d3012e7251f", "name": "Document Section Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [740, -260], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "doc_id", "value": "={{ $json.documentId }}"}]}}, "jsonData": "={{ $json.content }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "321222cb-1daf-4be2-a6ca-1a03d24f670f", "name": "Document Chunker", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [860, -100], "parameters": {"options": {"splitCode": "markdown"}, "chunkSize": 3000, "chunkOverlap": 200}, "typeVersion": 1}, {"id": "716519f5-cec1-4bfe-afbe-614fc23e74b5", "name": "MongoDB Vector Store Inserter", "type": "@n8n/n8n-nodes-langchain.vectorStoreMongoDBAtlas", "position": [540, -420], "parameters": {"mode": "insert", "options": {}, "mongoCollection": {"__rl": true, "mode": "list", "value": "n8n-template", "cachedResultName": "n8n-template"}, "vectorIndexName": "data_index"}, "credentials": {"mongoDb": {"id": "7riubYENUDZsmjyK", "name": "MongoDB account 2"}}, "typeVersion": 1.1}, {"id": "a49c19fc-f5f5-4381-b6ba-1bfc12b96135", "name": "OpenAI Embeddings Generator", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [480, -180], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "cJRah9hGPQ7eX4jd", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "6de724d5-2941-4e72-af8b-302ca2cf2ca0", "name": "Google Docs Importer", "type": "n8n-nodes-base.googleDocs", "position": [200, -420], "parameters": {"operation": "get", "documentURL": "https://docs.google.com/document/d/1gvgp71e9edob8WLqFIYCdzC7kUq3pLO37VKb-a-vVW4/edit?tab=t.0"}, "credentials": {"googleDocsOAuth2Api": {"id": "FNXMwqMf7vl1WUFj", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "4f30bb21-72f0-4d13-b610-2ec218ad31b1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-420, -440], "parameters": {"color": 5, "content": "Run this workflow manually to import and index Google Docs product documentation into MongoDB with vector embeddings for fast search."}, "typeVersion": 1}, {"id": "25fd33d5-041b-4f01-a46b-1bacabd88376", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [40, 0], "webhookId": "427ead97-647d-49c7-82d7-e76b40664fd1", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "f1f3fadd-d5e6-45df-b810-1616531dffcb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-420, 40], "parameters": {"color": 4, "content": "This workflow uses retrieval-augmented generation (RAG) to answer user questions by searching the MongoDB vector store and generating AI responses with context."}, "typeVersion": 1}, {"id": "39eee95c-b332-4ae4-bde9-aaf0fe5e0546", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1060, -380], "parameters": {"height": 520, "content": "Search Index Example \n\n{\n  \"mappings\": {\n    \"dynamic\": false,\n    \"fields\": {\n      \"_id\": {\n        \"type\": \"string\"\n      },\n      \"text\": {\n        \"type\": \"string\"\n      },\n      \"embedding\": {\n        \"type\": \"knnVector\",\n        \"dimensions\": 1536,\n        \"similarity\": \"cosine\"\n      },\n      \"source\": {\n        \"type\": \"string\"\n      },\n      \"doc_id\": {\n        \"type\": \"string\"\n      }\n    }\n  }\n}\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Simple Memory": {"ai_memory": [[{"node": "Knowledge Base Agent", "type": "ai_memory", "index": 0}]]}, "Document Chunker": {"ai_textSplitter": [[{"node": "Document Section Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "MongoDB Vector Search", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Knowledge Base Agent", "type": "ai_languageModel", "index": 0}]]}, "Google Docs Importer": {"main": [[{"node": "MongoDB Vector Store Inserter", "type": "main", "index": 0}]]}, "Knowledge Base Agent": {"main": [[]]}, "MongoDB Vector Search": {"ai_tool": [[{"node": "Knowledge Base Agent", "type": "ai_tool", "index": 0}]]}, "Document Section Loader": {"ai_document": [[{"node": "MongoDB Vector Store Inserter", "type": "ai_document", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Knowledge Base Agent", "type": "main", "index": 0}]]}, "OpenAI Embeddings Generator": {"ai_embedding": [[{"node": "MongoDB Vector Store Inserter", "type": "ai_embedding", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Google Docs Importer", "type": "main", "index": 0}]]}}}