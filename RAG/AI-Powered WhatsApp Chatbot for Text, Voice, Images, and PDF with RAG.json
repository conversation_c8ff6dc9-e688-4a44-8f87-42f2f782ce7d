{"meta": {"instanceId": "074f90e2bb5206c5f405a8aac6551497c72005283a5405fb08207b1b3a78c2b8", "templateCredsSetupCompleted": true}, "nodes": [{"id": "d1b47f60-5a4d-4629-a18d-b3c6ec0c972c", "name": "Knowledge Base Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [3720, 400], "parameters": {"text": "={{ $json.text }}", "options": {"systemMessage": ""}, "promptType": "define"}, "typeVersion": 1.9}, {"id": "c36731ce-5086-4a2f-8c3a-63be150fb8ae", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3720, 580], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "cJRah9hGPQ7eX4jd", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "53c2496a-0737-452c-b49c-ca0afa9c0073", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [4060, 740], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "cJRah9hGPQ7eX4jd", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "54697b14-28a0-4572-b17e-328a7b52cc90", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [1380, -560], "parameters": {}, "typeVersion": 1}, {"id": "a79b9b58-0211-4570-af84-64db8bef9de3", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [3820, 580], "parameters": {"sessionKey": "=memory_{{ $('WhatsApp Trigger').item.json.contacts[0].wa_id }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "fda97aec-70be-4611-94a3-7816e384c76b", "name": "MongoDB Vector Search", "type": "@n8n/n8n-nodes-langchain.vectorStoreMongoDBAtlas", "position": [3940, 580], "parameters": {"mode": "retrieve-as-tool", "options": {}, "toolName": "productDocs", "mongoCollection": {"__rl": true, "mode": "list", "value": "n8n-template", "cachedResultName": "n8n-template"}, "toolDescription": "retreive documentation", "vectorIndexName": "data_index"}, "credentials": {"mongoDb": {"id": "7riubYENUDZsmjyK", "name": "MongoDB account 2"}}, "typeVersion": 1.1}, {"id": "8cf54cb6-7247-4036-b1e4-9a28650d700a", "name": "Document Section Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1920, -400], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "doc_id", "value": "={{ $json.documentId }}"}]}}, "jsonData": "={{ $json.content }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "4e93e3c3-1847-483a-b6a7-67ff2cff294f", "name": "Document Chunker", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1920, -260], "parameters": {"options": {"splitCode": "markdown"}, "chunkSize": 3000, "chunkOverlap": 200}, "typeVersion": 1}, {"id": "4f616bfc-4ec7-4c88-8a63-cb2c47655680", "name": "MongoDB Vector Store Inserter", "type": "@n8n/n8n-nodes-langchain.vectorStoreMongoDBAtlas", "position": [1820, -560], "parameters": {"mode": "insert", "options": {}, "mongoCollection": {"__rl": true, "mode": "list", "value": "n8n-template", "cachedResultName": "n8n-template"}, "vectorIndexName": "data_index"}, "credentials": {"mongoDb": {"id": "7riubYENUDZsmjyK", "name": "MongoDB account 2"}}, "typeVersion": 1.1}, {"id": "cc9a0766-69a4-4745-97f5-4c4c5e156c91", "name": "OpenAI Embeddings Generator", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1820, -400], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "cJRah9hGPQ7eX4jd", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a9368d23-e35f-4986-8060-30a05f3f9c05", "name": "Google Docs Importer", "type": "n8n-nodes-base.googleDocs", "position": [1600, -560], "parameters": {"operation": "get", "documentURL": "https://docs.google.com/document/d/1gvgp71e9edob8WLqFIYCdzC7kUq3pLO37VKb-a-vVW4/edit?tab=t.0"}, "credentials": {"googleDocsOAuth2Api": {"id": "FNXMwqMf7vl1WUFj", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "1fb0f8a3-9113-4185-a935-0fc327f70881", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1000, -580], "parameters": {"color": 5, "content": "Run this workflow manually to import and index Google Docs product documentation into MongoDB with vector embeddings for fast search."}, "typeVersion": 1}, {"id": "62e13275-6f92-4799-9cbe-e0064ceb5140", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1000, 360], "parameters": {"color": 4, "height": 280, "content": "This workflow listens for WhatsApp messages (text, audio, image, documents), converts them into embeddings, searches MongoDB, and uses GPT-4o-mini to provide context-aware answers with conversation memory."}, "typeVersion": 1}, {"id": "d21fc408-f66d-4b07-972a-811aa420b4ce", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2220, -580], "parameters": {"height": 540, "content": "Search Index Example \n\n{\n  \"mappings\": {\n    \"dynamic\": false,\n    \"fields\": {\n      \"_id\": {\n        \"type\": \"string\"\n      },\n      \"text\": {\n        \"type\": \"string\"\n      },\n      \"embedding\": {\n        \"type\": \"knnVector\",\n        \"dimensions\": 1536,\n        \"similarity\": \"cosine\"\n      },\n      \"source\": {\n        \"type\": \"string\"\n      },\n      \"doc_id\": {\n        \"type\": \"string\"\n      }\n    }\n  }\n}\n"}, "typeVersion": 1}, {"id": "ddc0fdb7-bacc-4f41-87bf-48bb714a404d", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.whatsAppTrigger", "position": [1380, 400], "webhookId": "ab09ca3e-53b2-4079-9ddc-bccb2f69551b", "parameters": {"options": {}, "updates": ["messages"]}, "credentials": {"whatsAppTriggerApi": {"id": "eb1MSQfEla9dGghs", "name": "WhatsApp OAuth account"}}, "typeVersion": 1}, {"id": "8842d0c7-082e-4b4e-b350-a8bb8f47c390", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2460, 160], "parameters": {"options": {}, "resource": "audio", "operation": "translate"}, "credentials": {"openAiApi": {"id": "cJRah9hGPQ7eX4jd", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "a342dc14-0ffe-41ba-a3e9-853fe043f4fb", "name": "OpenAI1", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2460, 400], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {"detail": "auto"}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "cJRah9hGPQ7eX4jd", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "b1220587-fadf-4b5b-944e-b6e60b64c141", "name": "Extract from PDF", "type": "n8n-nodes-base.extractFromFile", "position": [3060, 860], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "b02474a0-be6a-4591-a10c-abc2f597a3e6", "name": "Extract from XLS", "type": "n8n-nodes-base.extractFromFile", "position": [2960, 1060], "parameters": {"options": {}, "operation": "xls"}, "typeVersion": 1}, {"id": "1012f8a7-9fcb-4d80-ad5f-bfaa31eddf7d", "name": "Extract from XLSX", "type": "n8n-nodes-base.extractFromFile", "position": [2960, 1220], "parameters": {"options": {}, "operation": "xlsx", "binaryPropertyName": "=data"}, "typeVersion": 1}, {"id": "8891a2eb-16d6-471a-803e-df1789a336f3", "name": "Map JSON", "type": "n8n-nodes-base.set", "position": [3200, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "72ae0d20-616a-4a65-9b14-53bf53656091", "name": "data", "type": "string", "value": "={{ $json }}"}]}}, "typeVersion": 3.4}, {"id": "a0920bc7-71bd-4803-bbce-4cef14cac5ee", "name": "Map file extensions", "type": "n8n-nodes-base.code", "position": [2420, 740], "parameters": {"jsCode": "let requests = $(\"Download Document\").all()\n\nrequests.forEach((request) => {\n  let mime_type = request.json.mime_type\n\n  if (\n    mime_type === \"text/calendar\" || \n    mime_type === \"application/ics\" || \n    mime_type === \"text/x-calendar\"\n  ) {\n    request.json.mime_type = \"mapped/calendar\"\n  }\n\n  if (\n    mime_type === \"application/xml\" || \n    mime_type === \"text/xml\") {\n    request.json.mime_type = \"mapped/xml\"\n  }\n\n  if (!mime_type) {\n    request.json.mime_type = $('Gets WhatsApp Document Source URL').first().json.mime_type\n  }\n})\n\nreturn requests;"}, "typeVersion": 2}, {"id": "77302aa3-6e87-4de6-9ec7-387ddb76fc84", "name": "Map document prompt", "type": "n8n-nodes-base.set", "position": [3440, 800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "da68bcca-a2a6-4676-8649-6fb1b664e44c", "name": "text", "type": "string", "value": "=Parsed text: {{ $json.text || $json.data || $json }}\n\nCaption text: {{ $('Route Types').item.json.messages[0].document.caption }}\n\nMimeType: {{ $('Gets WhatsApp Document Source URL').item.json.mime_type }}"}]}}, "typeVersion": 3.4}, {"id": "90821e49-c98d-4617-8289-1be6d3aeb087", "name": "Map image prompt", "type": "n8n-nodes-base.set", "position": [2680, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "48af2dcc-4ce9-45fc-abfc-54f803930092", "name": "text", "type": "string", "value": "=User image description: {{ $json.content }}\n\nUser image caption: {{ $('Route Types').item.json.messages[0].image.caption }}"}]}}, "typeVersion": 3.4}, {"id": "cf603f5c-0a6e-4cdd-846b-738ed417daa2", "name": "Map text prompt", "type": "n8n-nodes-base.set", "position": [2020, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "38aec976-a32c-4b0e-85f4-c90adc16abc9", "name": "text", "type": "string", "value": "={{ $json.messages[0].text.body }}"}]}}, "typeVersion": 3.4}, {"id": "7727e89d-cf21-4440-ab7f-85594eb1ec44", "name": "Gets WhatsApp Voicemail Source URL", "type": "n8n-nodes-base.whatsApp", "position": [2020, 160], "webhookId": "bbe62f3d-8788-49d4-aae6-9e9411446d44", "parameters": {"resource": "media", "operation": "mediaUrlGet", "mediaGetId": "={{ $json.messages[0].audio.id}}"}, "credentials": {"whatsAppApi": {"id": "HuYOJ00weACKEFR6", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "08b938a1-d32e-4aa1-958f-bdabd3dfec13", "name": "Gets WhatsApp Image Source URL", "type": "n8n-nodes-base.whatsApp", "position": [2020, 400], "webhookId": "c2982df4-1d8d-4669-a724-44ae17d11e6c", "parameters": {"resource": "media", "operation": "mediaUrlGet", "mediaGetId": "={{ $json.messages[0].image.id }}"}, "credentials": {"whatsAppApi": {"id": "HuYOJ00weACKEFR6", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "d934ce80-cdb0-476d-8896-b8c226086933", "name": "Gets WhatsApp Document Source URL", "type": "n8n-nodes-base.whatsApp", "position": [2020, 740], "webhookId": "c2982df4-1d8d-4669-a724-44ae17d11e6c", "parameters": {"resource": "media", "operation": "mediaUrlGet", "mediaGetId": "={{ $json.messages[0].document.id }}"}, "credentials": {"whatsAppApi": {"id": "HuYOJ00weACKEFR6", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "aad1b221-0985-43e7-bc0b-312436171abe", "name": "Download Voicemail", "type": "n8n-nodes-base.httpRequest", "position": [2240, 160], "parameters": {"url": "={{ $json.url }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "VZo8qdSLzpZ3qRlq", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "dfd08bec-9d30-4125-b167-7feaeefd7afa", "name": "Download Image", "type": "n8n-nodes-base.httpRequest", "position": [2240, 400], "parameters": {"url": "={{ $json.url }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "VZo8qdSLzpZ3qRlq", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "ba3d221f-ce5c-4105-82cf-fbd11c2c0e2f", "name": "Download Document", "type": "n8n-nodes-base.httpRequest", "position": [2240, 740], "parameters": {"url": "={{ $json.url }}", "options": {"response": {"response": {}}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "VZo8qdSLzpZ3qRlq", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "87ecc44f-cc1f-47e0-b995-079230be118d", "name": "Route Types", "type": "n8n-nodes-base.switch", "position": [1600, 380], "parameters": {"rules": {"values": [{"outputKey": "Text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2fc5c912-629b-4cbe-b5e3-7e3f0651c628", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.messages[0].type }}", "rightValue": "text"}]}, "renameOutput": true}, {"outputKey": "Audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "26a3d85c-0815-48ff-85ce-713129a1107c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.messages[0].type }}", "rightValue": "audio"}]}, "renameOutput": true}, {"outputKey": "Image", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "840b95b8-6559-4fb7-b32c-651451d6d0d2", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.messages[0].type }}", "rightValue": "image"}]}, "renameOutput": true}, {"outputKey": "Document", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3e7a07f9-b785-450c-8c68-f6b276838503", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.messages[0].type }}", "rightValue": "document"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "ce3d08ce-4445-4473-90b2-227ba065c4d8", "name": "Route Document Types", "type": "n8n-nodes-base.switch", "position": [2680, 580], "parameters": {"rules": {"values": [{"outputKey": "CSV", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "14e23243-cd44-4cb1-99bd-9e6905d511ad", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "text/csv"}]}, "renameOutput": true}, {"outputKey": "HTML", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6d7616c5-6bdd-47b7-923e-639491d15a4e", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "text/html"}]}, "renameOutput": true}, {"outputKey": "Calendar", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a2174e02-378a-41ff-b269-61f4fc3f1de9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "=mapped/calendar"}]}, "renameOutput": true}, {"outputKey": "RTF", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f3b406d7-362d-473e-8edd-c3e5f2d9c44c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "text/rtf"}]}, "renameOutput": true}, {"outputKey": "TXT", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "64dd4658-54e7-4453-adbc-7067dffcd555", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "text/plain"}]}, "renameOutput": true}, {"outputKey": "XML", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7540a3ab-b48e-4bec-94e9-a5dfc3d65a4c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "mapped/xml"}]}, "renameOutput": true}, {"outputKey": "PDF", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "88b618fd-9a88-491e-91dd-c5fc9efa36e3", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "application/pdf"}]}, "renameOutput": true}, {"outputKey": "JSON", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9c4d90aa-b4ea-4a63-b15e-666899c8360e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "application/json"}]}, "renameOutput": true}, {"outputKey": "XLS", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9baa7c88-3950-4099-8498-99a4640b95e7", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "application/vnd.ms-excel"}]}, "renameOutput": true}, {"outputKey": "XLSX", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b83e540c-ba1e-42d0-ac83-f675e25e6aea", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mime_type }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}, "renameOutput": true}, {"outputKey": "ELSE", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ea3be820-2ead-4ec2-b292-42d3c7804b55", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.mime_type }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "********-23ec-4e10-88f4-e93363432a67", "name": "Send Unsupported Response", "type": "n8n-nodes-base.whatsApp", "position": [2960, 1380], "webhookId": "017d267f-4897-4726-bf03-304ef10352bf", "parameters": {"textBody": "=The File type you provided is unsupported.", "operation": "send", "phoneNumberId": "***************", "additionalFields": {}, "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}"}, "credentials": {"whatsAppApi": {"id": "HuYOJ00weACKEFR6", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "acc8e4ae-0610-42fc-a318-b9a23a38919b", "name": "Send Response", "type": "n8n-nodes-base.whatsApp", "position": [4080, 400], "webhookId": "********-5066-48ba-8e19-549680df2b27", "parameters": {"textBody": "={{ $json.output }}", "operation": "send", "phoneNumberId": "***************", "additionalFields": {}, "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}"}, "credentials": {"whatsAppApi": {"id": "HuYOJ00weACKEFR6", "name": "WhatsApp account"}}, "typeVersion": 1}], "pinData": {}, "connections": {"OpenAI": {"main": [[{"node": "Knowledge Base Agent", "type": "main", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "Map image prompt", "type": "main", "index": 0}]]}, "Map JSON": {"main": [[{"node": "Map document prompt", "type": "main", "index": 0}]]}, "Route Types": {"main": [[{"node": "Map text prompt", "type": "main", "index": 0}], [{"node": "Gets WhatsApp Voicemail Source URL", "type": "main", "index": 0}], [{"node": "Gets WhatsApp Image Source URL", "type": "main", "index": 0}], [{"node": "Gets WhatsApp Document Source URL", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Knowledge Base Agent", "type": "ai_memory", "index": 0}]]}, "Download Image": {"main": [[{"node": "OpenAI1", "type": "main", "index": 0}]]}, "Map text prompt": {"main": [[{"node": "Knowledge Base Agent", "type": "main", "index": 0}]]}, "Document Chunker": {"ai_textSplitter": [[{"node": "Document Section Loader", "type": "ai_textSplitter", "index": 0}]]}, "Extract from PDF": {"main": [[{"node": "Map document prompt", "type": "main", "index": 0}]]}, "Extract from XLS": {"main": [[{"node": "Map JSON", "type": "main", "index": 0}]]}, "Map image prompt": {"main": [[{"node": "Knowledge Base Agent", "type": "main", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "Route Types", "type": "main", "index": 0}]]}, "Download Document": {"main": [[{"node": "Map file extensions", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "MongoDB Vector Search", "type": "ai_embedding", "index": 0}]]}, "Extract from XLSX": {"main": [[{"node": "Map JSON", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Knowledge Base Agent", "type": "ai_languageModel", "index": 0}]]}, "Download Voicemail": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Map document prompt": {"main": [[{"node": "Knowledge Base Agent", "type": "main", "index": 0}]]}, "Map file extensions": {"main": [[{"node": "Route Document Types", "type": "main", "index": 0}]]}, "Google Docs Importer": {"main": [[{"node": "MongoDB Vector Store Inserter", "type": "main", "index": 0}]]}, "Knowledge Base Agent": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}, "Route Document Types": {"main": [[{"node": "Map document prompt", "type": "main", "index": 0}], [{"node": "Map document prompt", "type": "main", "index": 0}], [{"node": "Map document prompt", "type": "main", "index": 0}], [{"node": "Map document prompt", "type": "main", "index": 0}], [{"node": "Map document prompt", "type": "main", "index": 0}], [{"node": "Map document prompt", "type": "main", "index": 0}], [{"node": "Extract from PDF", "type": "main", "index": 0}], [{"node": "Map JSON", "type": "main", "index": 0}], [{"node": "Extract from XLS", "type": "main", "index": 0}], [{"node": "Extract from XLSX", "type": "main", "index": 0}], [{"node": "Send Unsupported Response", "type": "main", "index": 0}]]}, "MongoDB Vector Search": {"ai_tool": [[{"node": "Knowledge Base Agent", "type": "ai_tool", "index": 0}]]}, "Document Section Loader": {"ai_document": [[{"node": "MongoDB Vector Store Inserter", "type": "ai_document", "index": 0}]]}, "Send Unsupported Response": {"main": [[]]}, "OpenAI Embeddings Generator": {"ai_embedding": [[{"node": "MongoDB Vector Store Inserter", "type": "ai_embedding", "index": 0}]]}, "Gets WhatsApp Image Source URL": {"main": [[{"node": "Download Image", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Google Docs Importer", "type": "main", "index": 0}]]}, "Gets WhatsApp Document Source URL": {"main": [[{"node": "Download Document", "type": "main", "index": 0}]]}, "Gets WhatsApp Voicemail Source URL": {"main": [[{"node": "Download Voicemail", "type": "main", "index": 0}]]}}}