{"id": "EQVZKxcCDzTNXLRp", "meta": {"instanceId": "2a26454b0172ffcb8d70ba77c235b1209f92cd71bf06c79ba609c7173b416d68", "templateCredsSetupCompleted": true}, "name": "Chat with PDF / MD / Text Files using GraphRAG (no vector store needed)", "tags": [{"id": "66wgFoDi9Xjl74M3", "name": "Support", "createdAt": "2025-05-21T17:06:32.355Z", "updatedAt": "2025-05-21T17:06:32.355Z"}, {"id": "kRM0hQV2zw7VxrON", "name": "Research", "createdAt": "2025-05-21T19:44:19.136Z", "updatedAt": "2025-05-21T19:44:19.136Z"}, {"id": "sJk9cUvmMU8FkJXv", "name": "AI", "createdAt": "2025-05-20T13:16:15.636Z", "updatedAt": "2025-05-20T13:16:15.636Z"}], "nodes": [{"id": "011910a9-c2a0-49eb-b1eb-8043e0c2accc", "name": "Search Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [-220, -120], "parameters": {"filter": {"folderId": {"__rl": true, "mode": "list", "value": "13tqp0SaI_v4jG1CFLAZo96isx-UBno4v", "cachedResultUrl": "https://drive.google.com/drive/folders/13tqp0SaI_v4jG1CFLAZo96isx-UBno4v", "cachedResultName": "GraphRAG"}}, "options": {}, "resource": "fileFolder", "returnAll": true, "queryString": "*"}, "credentials": {"googleDriveOAuth2Api": {"id": "oz9iNMxcuQ2pxr1e", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "22872ac5-14e4-4ea7-b792-30e19a02cb88", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [260, -120], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "6456b7ac-f936-425a-a0ac-62fe118e985a", "name": "Retrieve File", "type": "n8n-nodes-base.googleDrive", "position": [500, -80], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "oz9iNMxcuQ2pxr1e", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "8c1f2da9-fff4-457d-bc2e-7c84de3500b3", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [740, -20], "parameters": {"rules": {"values": [{"outputKey": "pdf", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7b4e792b-ab6d-4b9b-88a1-d8e51bea6853", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{$binary[\"data\"].mimeType}}", "rightValue": "application/pdf"}]}, "renameOutput": true}, {"outputKey": "text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "09b7d7c5-5353-4719-b4e2-072e4da39948", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{$binary[\"data\"].mimeType}}", "rightValue": "text/plain"}]}, "renameOutput": true}, {"outputKey": "md", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "cb2e6726-97d8-4541-8383-deafff9b18e6", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{$binary[\"data\"].mimeType}}", "rightValue": "text/markdown"}]}, "renameOutput": true}, {"outputKey": "json", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d2763a45-a592-47c8-868f-59dfcd17a71c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{$binary[\"data\"].mimeType}}", "rightValue": "application/json"}]}, "renameOutput": true}, {"outputKey": "docs", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0d9b561f-89c5-479e-a4e2-1f5f05fa8417", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{$binary[\"data\"].mimeType}}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "csv", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6b19deed-5d00-4796-bb9d-b4d87564a751", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{$binary[\"data\"].mimeType}}", "rightValue": "csv"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "fe133ad7-bd7a-43e2-91a7-4b3dd2652490", "name": "Extract from PDF", "type": "n8n-nodes-base.extractFromFile", "position": [1020, -240], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "49f89141-a8ea-46c6-888d-cf9dafcc7c2a", "name": "Extract from Text File", "type": "n8n-nodes-base.extractFromFile", "position": [1020, -60], "parameters": {"options": {}, "operation": "text"}, "typeVersion": 1}, {"id": "a47d8f07-917e-44ed-a69e-5d6085981d36", "name": "Extract from Markdown", "type": "n8n-nodes-base.extractFromFile", "position": [1020, 140], "parameters": {"options": {}, "operation": "text"}, "typeVersion": 1}, {"id": "a2298650-5408-45f8-accf-292ac669002a", "name": "InfraNodus Save to Graph", "type": "n8n-nodes-base.httpRequest", "position": [1480, 200], "parameters": {"url": "https://infranodus.com/api/v1/graphAndStatements?doNotSave=false&includeGraph=false&includeGraphSummary=true&includeGraph=false", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "name", "value": "graphrag_from_google_drive"}, {"name": "text", "value": "={{ $json.data }}"}, {"name": "=categories", "value": "=[filename: {{ $('Switch').item.json.name }}]"}, {"name": "contextSettings", "value": "={{{ \"squareBracketsProcessing\":\"IGNORE_BRACKETS\"} }}"}]}, "genericAuthType": "httpBearerAuth"}, "credentials": {"httpBearerAuth": {"id": "FPDx6PA5CtzGEIQc", "name": "InfraNodus DeeMeeTree API Key"}}, "typeVersion": 4.2}, {"id": "bb06d60a-01c7-4a1e-bc7a-901e1f7f175d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-560, -480], "parameters": {"color": 4, "width": 2200, "height": 980, "content": "# Step 1: Upload Your PDF / MD / Text Files to InfraNodus GraphRAG\n\n## Copy your PDF files to a Google drive and then upload them to your InfraNodus knowledge graph\n\n### InfraNodus visualizes your text as a knowledge graph, showing the main topics and ideas inside. It also provides API access to the knowledge graph, so you can use it as a knowledge base instead of the complex vector store setup\n\n1. You need an [InfraNodus](https://infranodus.com) account to use this workflow. Get the API key at [https://infranodus.com/api-access](https://infranodus.com/api-access)\n2. Also, specify the **name of the graph** you want to save the files to in the InfraNodus HTTP node\n3. When the workflow finishes running, you can get a visualization of your knowledge base at [https://infranodus.com/your_user_name/your_graph_name/edit](https://infranodus.com/your_user_name/your_graph_name/edit)\n\n\n"}, "typeVersion": 1}, {"id": "38707a4d-b397-43e7-b55c-da18a00bb480", "name": "Map PDF to Text", "type": "n8n-nodes-base.set", "position": [1320, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "fd160009-0b92-46fc-9e34-a1283b810e91", "name": "data", "type": "string", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "49426e56-7d6c-4dc0-a279-348dea59cca2", "name": "Convert File to PDF", "type": "n8n-nodes-base.httpRequest", "disabled": true, "position": [1720, 200], "parameters": {"url": "https://v2.convertapi.com/convert/pdf/to/txt", "method": "POST", "options": {"response": {"response": {"responseFormat": "text"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "genericAuthType": "httpBearerAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpBearerAuth": {"id": "mDxLoJUWSmuTJsAC", "name": "ConvertAPI"}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "5d21b250-7b0f-4925-a858-5aa6badf5700", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1680, -340], "parameters": {"color": 2, "width": 360, "height": 840, "content": "## Optional: Better PDF Conversion\n\n### Standard Map PDF to Text node will split your PDF files into very short chunks, which deteriorates retrieval. \n\nUse can use [ConvertAPI](https://convertapi.com?ref=4l54n) which is a high-quality convertor that will respect the layout of the original document and not cut the paragraphs into short chunks. \n\nHere is an HTTP node that makes a request to their API to convert the PDF into text. If you have a ConvertAPI account, you can replace the \"Map PDF to Text\" node in step 4 with this node. \n"}, "typeVersion": 1}, {"id": "ceda9ea4-963b-4ffb-a741-6e7eb3fc7411", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "disabled": true, "position": [-520, 840], "webhookId": "a2cfd3b0-aaa4-4003-940d-e520e64830c6", "parameters": {"public": true, "options": {"title": "Talk to PDFs"}, "initialMessages": "=Ask any question about the PDFs or write `/question` to generate an interesting question to discuss."}, "typeVersion": 1.1}, {"id": "d78cf21e-753b-44d3-a2b4-493f85aa5129", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-240, 840], "parameters": {"options": {"systemMessage": "You provide information about the topic of user's interest. Always use the knowledge base attached to get the final response. "}}, "typeVersion": 1.9}, {"id": "02497c8b-89d6-4440-bc4d-d3906b9c4872", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-420, 1100], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "07wFa8Wa4mMRCHAj", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "6d77fb59-fb03-4d22-ac40-4cf9bde25909", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-260, 1100], "parameters": {}, "typeVersion": 1.3}, {"id": "978c8309-380d-4fc8-b238-e0d0148b77b9", "name": "Knowledge Base GraphRAG", "type": "n8n-nodes-base.httpRequestTool", "position": [100, 1060], "parameters": {"url": "https://infranodus.com/api/v1/graphAndAdvice?doNotSave=true&addStats=true&optimize=develop&includeStatements=true&includeGraphSummary=true&includeGraph=false", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "name", "value": "graphrag_from_google_drive"}, {"name": "requestMode", "value": "response"}, {"name": "prompt", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters2_Value', `User query to send to the expert`, 'string') }}"}, {"name": "aiTopics", "value": "true"}]}, "genericAuthType": "httpBearerAuth", "toolDescription": "You contain knowledge on the topic of user's interst"}, "credentials": {"httpBearerAuth": {"id": "6lSuuSDajZrvI2GM", "name": "InfraNodus API Key"}}, "typeVersion": 4.2}, {"id": "b005f83d-3d4a-42c8-b70a-63e267747fcd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-560, 540], "parameters": {"width": 2200, "height": 700, "content": "# Step 2: Chat with your PDF / MD / Text Files using [InfraNodus](https://infranodus.com) Graph RAG Knowledge Base\n\n## InfraNodus is used instead of the vector store. It combines traditional RAG and GraphRAG, providing a better topical overview of your knowledge base and having a better understanding of relations between entities in your documents.\n\n1. Deactivate the trigger node in Step 1 and activate the trigger Chat node here\n2. Provide your API key at [https://infranodus.com/api-access](https://infranodus.com/api-access) to the HTTP agent tool\n3. Specify the same **name of the graph** as in the Step 1, so you query the graph where saved the files. \n4. If you connect several knowledge bases, make sure to describe them well, so the agent knows what to query. Get the description from the InfraNodus Graph > Project Notes > RAG enhancement"}, "typeVersion": 1}, {"id": "6a6ad150-670d-45d5-a69e-0bafaf8f2ac4", "name": "Click ‘Test workflow’ to ingest the documents", "type": "n8n-nodes-base.manualTrigger", "position": [-500, -120], "parameters": {}, "typeVersion": 1}, {"id": "0a148a3f-e898-4f83-9ebb-82da7f3f4633", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-540, 100], "parameters": {"width": 680, "height": 380, "content": "## [InfraNodus](https://infranodus.com) Knowledege Graph Example\n\n![InfraNodus knowledge graph](https://infranodus.com/images/front/infranodus-overview.jpg)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "90ea1bad-6d21-4f20-81b4-0ba48cc7e7fa", "connections": {"Switch": {"main": [[{"node": "Extract from PDF", "type": "main", "index": 0}], [{"node": "Extract from Text File", "type": "main", "index": 0}], [{"node": "Extract from Markdown", "type": "main", "index": 0}], [], []]}, "Retrieve File": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Retrieve File", "type": "main", "index": 0}]]}, "Map PDF to Text": {"main": [[{"node": "InfraNodus Save to Graph", "type": "main", "index": 0}]]}, "Extract from PDF": {"main": [[{"node": "Map PDF to Text", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Search Google Drive": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract from Markdown": {"main": [[{"node": "InfraNodus Save to Graph", "type": "main", "index": 0}]]}, "Extract from Text File": {"main": [[{"node": "InfraNodus Save to Graph", "type": "main", "index": 0}]]}, "Knowledge Base GraphRAG": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "InfraNodus Save to Graph": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Click ‘Test workflow’ to ingest the documents": {"main": [[{"node": "Search Google Drive", "type": "main", "index": 0}]]}}}