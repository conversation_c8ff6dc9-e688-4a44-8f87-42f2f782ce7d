<!--
 * @Author: kangrl
 * @Email: <EMAIL>
 * @Date: 2025-07-02 20:17:23
 * @LastEditors: kangrl
 * @LastEditTime: 2025-07-02 20:25:34
 * @FilePath: /Ain8n/deep.html
 * Copyright (C) 2025 by kangrl, All Rights Reserved.
 * @Description:
-->
<!-- 直接渲染内容，避免依赖视频加载和内联事件处理器 -->
<div class="form-group" style="margin-bottom:16px;">
  <label class="form-label" for="breadth">
    Enter research breadth (Default 2)
  </label>
  <p style="font-size:12px;color:#666;text-align:left">
    This value determines how many sources to explore.
  </p>
  <input
    class="form-input"
    type="range"
    id="breadth"
    name="input-breadth"
    value="2"
    step="1"
    max="5"
    min="1"
    list="breadth-markers"
  >
  <datalist
    id="breadth-markers"
    style="display: flex;
    flex-direction: row;
    justify-content: space-between;
    writing-mode: horizontal-tb;
    margin-top: -10px;
    text-align: center;
    font-size: 10px;
    margin-left: 16px;
    margin-right: 16px;"
  >
    <option value="1" label="1"></option>
    <option value="2" label="2"></option>
    <option value="3" label="3"></option>
    <option value="4" label="4"></option>
    <option value="5" label="5"></option>
  </datalist>
  <!-- 隐藏的输入字段，用于确保正确的值传递 -->
  <input type="hidden" name="input-breadth" id="breadth-value" value="2">
</div>

<script>
// 确保range slider的值能正确传递给下游系统
document.addEventListener('DOMContentLoaded', function() {
  const breadthSlider = document.getElementById('breadth');
  const breadthHidden = document.getElementById('breadth-value');

  if (breadthSlider && breadthHidden) {
    // 初始化隐藏字段的值
    breadthHidden.value = breadthSlider.value;

    // 监听slider变化
    breadthSlider.addEventListener('input', function() {
      breadthHidden.value = this.value;
    });
  }
});
</script>

