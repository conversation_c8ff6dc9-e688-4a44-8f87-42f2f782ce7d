<!--
 * @Author: kangrl
 * @Email: <EMAIL>
 * @Date: 2025-07-02 20:17:23
 * @LastEditors: kangrl
 * @LastEditTime: 2025-07-02 20:25:34
 * @FilePath: /Ain8n/deep.html
 * Copyright (C) 2025 by kangrl, All Rights Reserved.
 * @Description:
-->
<video
    style="display:none"
    src="/when_will_n8n_support_range_sliders.mp4"
    onerror='
  this.insertAdjacentHTML(`afterend`,
  `<div class="form-group" style="margin-bottom:16px;">
    <label class="form-label" for="breadth">
      Enter research breadth (Default 2)
    </label>
    <p style="font-size:12px;color:#666;text-align:left">
      This value determines how many sources to explore.
    </p>
    <input
      class="form-input"
      type="range"
      id="breadth"
      name="field-2"
      value="2"
      step="1"
      max="5"
      min="1"
      list="breadth-markers"
    >
    <datalist
      id="breadth-markers"
      style="display: flex;
      flex-direction: row;
      justify-content: space-between;
      writing-mode: horizontal-tb;
      margin-top: -10px;
      text-align: center;
      font-size: 10px;
      margin-left: 16px;
      margin-right: 16px;"
    >
      <option value="1" label="1"></option>
      <option value="2" label="2"></option>
      <option value="3" label="3"></option>
      <option value="4" label="4"></option>
      <option value="5" label="5"></option>
    </datalist>
  </div>`)
  '
/>

